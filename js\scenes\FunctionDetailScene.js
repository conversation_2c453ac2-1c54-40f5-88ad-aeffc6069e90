/**
 * 功法详情场景类
 * 展示功法的节点和升级功能
 */
import BaseScene from './BaseScene';
import Button from '../ui/Button';
import game from '../../game';
import SkillConfig from '../config/SkillConfig';

class FunctionDetailScene extends BaseScene {
  constructor(ctx, screenWidth, screenHeight, sceneManager, resources) {
    super(ctx, screenWidth, screenHeight, sceneManager);

    // 场景资源
    this.resources = resources || {};

    // 当前功法信息
    this.currentSkillId = null;
    this.currentSkillName = null;
    this.currentRealm = null;

    // 功法节点数据
    this.nodes = [];

    // 节点位置信息
    this.nodePositions = [];

    // 当前玩家功法等级
    this.playerSkillLevel = 0;

    // 当前显示的层数（从1开始）
    this.currentLayer = 1;

    // 功法最大层数
    this.maxLayers = 3;
  }

  // 初始化UI
  initUI() {
    // 清空UI元素
    this.clearUIElements();

    // 创建返回按钮
    const buttonWidth = 80;
    const buttonHeight = 40;
    const margin = 10;

    this.backButton = new Button(
      this.ctx,
      margin,
      margin,
      buttonWidth,
      buttonHeight,
      '返回',
      null,
      null,
      () => {
        // 返回到功法页面
        this.sceneManager.showScene('skill');
      }
    );

    this.addUIElement(this.backButton);

    // 获取玩家功法等级
    this.playerSkillLevel = this.getPlayerSkillLevel();

    // 初始化节点位置
    this.initNodePositions();

    // 创建节点按钮
    this.createNodeButtons();

    // 创建修炼按钮
    this.createUpgradeButton();

    // 创建层数切换按钮
    this.createLayerButtons();
  }

  // 初始化节点位置
  initNodePositions() {
    const headerHeight = 120;
    const nodeSize = 50;
    const margin = 20;

    this.nodePositions = [];

    // 创建简单的连线布局：两行，每行5个节点
    const startX = margin + nodeSize;
    const startY = headerHeight + 80;
    const horizontalSpacing = (this.screenWidth - 2 * margin - nodeSize) / 4; // 5个节点，4个间隔
    const verticalSpacing = 100;

    // 第一行：节点1-5
    for (let i = 0; i < 5; i++) {
      const x = startX + i * horizontalSpacing - nodeSize / 2;
      const y = startY;

      this.nodePositions.push({
        x: x,
        y: y,
        size: nodeSize,
        centerX: x + nodeSize / 2,
        centerY: y + nodeSize / 2
      });
    }

    // 第二行：节点6-10
    for (let i = 0; i < 5; i++) {
      const x = startX + i * horizontalSpacing - nodeSize / 2;
      const y = startY + verticalSpacing;

      this.nodePositions.push({
        x: x,
        y: y,
        size: nodeSize,
        centerX: x + nodeSize / 2,
        centerY: y + nodeSize / 2
      });
    }
  }

  // 创建节点按钮
  createNodeButtons() {
    if (!this.currentSkillId) return;

    // 获取功法数据
    const skillData = SkillConfig.getSkillData(this.currentSkillId);
    if (!skillData) return;

    // 计算当前层的等级范围
    const layerStartLevel = (this.currentLayer - 1) * 10 + 1;
    const layerEndLevel = this.currentLayer * 10;

    // 创建10个节点按钮
    for (let i = 0; i < 10; i++) {
      const pos = this.nodePositions[i];
      const actualLevel = layerStartLevel + i; // 实际等级
      const displayLevel = i + 1; // 显示等级（1-10）

      // 检查节点状态
      const isUnlocked = this.playerSkillLevel >= actualLevel;
      const canUnlock = this.playerSkillLevel === actualLevel - 1;

      let buttonColor;
      let buttonText = displayLevel.toString();

      if (isUnlocked) {
        buttonColor = '#FFD700'; // 已点亮 - 金色
      } else if (canUnlock) {
        buttonColor = '#00FF00'; // 可点亮 - 绿色
      } else {
        buttonColor = 'rgba(100, 100, 100, 0.7)'; // 不可点亮 - 灰色
      }

      this.nodes[i] = new Button(
        this.ctx,
        pos.x,
        pos.y,
        pos.size,
        pos.size,
        buttonText,
        null,
        buttonColor,
        () => {
          if (canUnlock) {
            // 尝试升级功法
            this.upgradeSkill(actualLevel);
          } else if (isUnlocked) {
            // 显示节点属性
            this.showNodeAttributes(actualLevel);
          } else {
            // 显示提示
            console.log('需要先点亮前置节点');
          }
        }
      );

      this.addUIElement(this.nodes[i]);
    }
  }

  // 创建修炼按钮
  createUpgradeButton() {
    if (!this.currentSkillId || this.playerSkillLevel >= 30) return;

    const skillData = SkillConfig.getSkillData(this.currentSkillId);
    if (!skillData) return;

    const nextLevel = this.playerSkillLevel + 1;
    const nextLevelData = SkillConfig.getSkillLevelData(this.currentSkillId, nextLevel);

    if (nextLevelData) {
      const buttonWidth = 120;
      const buttonHeight = 50;

      this.upgradeButton = new Button(
        this.ctx,
        this.screenWidth / 2 - buttonWidth / 2,
        this.screenHeight - 120,
        buttonWidth,
        buttonHeight,
        '修炼',
        null,
        '#4CAF50',
        () => {
          this.upgradeSkill(nextLevel);
        }
      );

      this.addUIElement(this.upgradeButton);
    }
  }

  // 创建层数切换按钮
  createLayerButtons() {
    const buttonWidth = 80;
    const buttonHeight = 40;
    const margin = 20;

    // 上一层按钮
    this.prevLayerButton = new Button(
      this.ctx,
      this.screenWidth - buttonWidth - margin,
      this.screenHeight - buttonHeight * 2 - margin * 2,
      buttonWidth,
      buttonHeight,
      '上一层',
      null,
      this.currentLayer > 1 ? '#2196F3' : 'rgba(100, 100, 100, 0.7)',
      () => {
        if (this.currentLayer > 1) {
          this.currentLayer--;
          this.initUI();
        }
      }
    );

    this.addUIElement(this.prevLayerButton);

    // 下一层按钮
    this.nextLayerButton = new Button(
      this.ctx,
      this.screenWidth - buttonWidth - margin,
      this.screenHeight - buttonHeight - margin,
      buttonWidth,
      buttonHeight,
      '下一层',
      null,
      this.currentLayer < this.maxLayers ? '#2196F3' : 'rgba(100, 100, 100, 0.7)',
      () => {
        if (this.currentLayer < this.maxLayers) {
          this.currentLayer++;
          this.initUI();
        }
      }
    );

    this.addUIElement(this.nextLayerButton);
  }

  // 获取玩家功法等级
  getPlayerSkillLevel() {
    try {
      const player = game.gameStateManager.getPlayer();
      if (player && player.skills && player.skills[this.currentSkillId]) {
        return player.skills[this.currentSkillId].level || 0;
      }
      return 0;
    } catch (error) {
      console.error('获取玩家功法等级失败:', error);
      return 0;
    }
  }

  // 升级功法
  upgradeSkill(targetLevel) {
    const skillData = SkillConfig.getSkillData(this.currentSkillId);
    const levelData = SkillConfig.getSkillLevelData(this.currentSkillId, targetLevel);

    if (!skillData || !levelData) {
      console.error('功法数据不存在');
      return;
    }

    // 检查修炼点是否足够
    const player = game.gameStateManager.getPlayer();
    const functionPoints = player.resources && player.resources.functionPoints ? player.resources.functionPoints : 0;

    if (functionPoints < levelData.cost) {
      console.log(`修炼点不足，需要${levelData.cost}点，当前${functionPoints}点`);
      return;
    }

    // 扣除修炼点
    player.resources.functionPoints -= levelData.cost;

    // 更新功法等级
    if (!player.skills) player.skills = {};
    if (!player.skills[this.currentSkillId]) {
      player.skills[this.currentSkillId] = { level: 0 };
    }
    player.skills[this.currentSkillId].level = targetLevel;

    // 保存玩家数据
    game.gameStateManager.setPlayer(player);

    // 重新初始化UI
    this.playerSkillLevel = targetLevel;
    this.initUI();

    console.log(`功法${skillData.name}升级到${targetLevel}级成功`);
  }

  // 显示节点属性
  showNodeAttributes(level) {
    const skillData = SkillConfig.getSkillData(this.currentSkillId);
    const levelData = SkillConfig.getSkillLevelData(this.currentSkillId, level);

    if (!skillData || !levelData) return;

    // 显示属性信息
    let attributeText = `${skillData.name} 第${level}层属性:\n`;

    for (const [attr, value] of Object.entries(levelData.attributes)) {
      const attrName = this.getAttributeName(attr);
      attributeText += `${attrName}: +${value}\n`;
    }

    console.log(attributeText);
  }

  // 获取属性中文名称
  getAttributeName(attr) {
    const attrMap = {
      'hp': '生命值',
      'attack': '攻击力',
      'defense': '防御力',
      'speed': '速度',
      'critical': '暴击率'
    };
    return attrMap[attr] || attr;
  }

  // 场景显示时的回调
  onShow(params) {
    // 从参数中获取功法信息
    if (params) {
      this.currentSkillId = params.skillId;
      this.currentSkillName = params.skillName;
      this.currentRealm = params.realm;
    }

    // 初始化UI
    this.initUI();
  }

  // 场景隐藏时的回调
  onHide() {
    // 清空UI元素
    this.clearUIElements();
  }

  // 绘制场景
  drawScene() {
    // 绘制背景
    this.drawBackground();

    // 绘制标题
    this.drawTitle();

    // 绘制连接线
    this.drawConnections();

    // 绘制功法信息
    this.drawSkillInfo();
  }

  // 绘制背景
  drawBackground() {
    // 绘制渐变背景
    const gradient = this.ctx.createLinearGradient(0, 0, 0, this.screenHeight);
    gradient.addColorStop(0, 'rgba(20, 30, 60, 0.95)');
    gradient.addColorStop(0.5, 'rgba(40, 20, 60, 0.95)');
    gradient.addColorStop(1, 'rgba(10, 15, 30, 0.95)');
    this.ctx.fillStyle = gradient;
    this.ctx.fillRect(0, 0, this.screenWidth, this.screenHeight);
  }

  // 绘制标题
  drawTitle() {
    if (!this.currentSkillName) return;

    const headerHeight = 80;

    // 绘制功法名称
    this.ctx.font = 'bold 24px Arial';
    this.ctx.fillStyle = '#FFD700';
    this.ctx.textAlign = 'center';
    this.ctx.fillText(this.currentSkillName, this.screenWidth / 2, 40);

    // 绘制当前层数
    this.ctx.font = 'bold 18px Arial';
    this.ctx.fillStyle = '#00FFFF';
    this.ctx.fillText(`第${this.currentLayer}层`, this.screenWidth / 2, 65);

    // 绘制功法等级和修炼点信息
    const skillData = SkillConfig.getSkillData(this.currentSkillId);
    if (skillData) {
      this.ctx.font = '16px Arial';
      this.ctx.fillStyle = '#FFFFFF';
      this.ctx.fillText(`当前等级: ${this.playerSkillLevel}/30`, this.screenWidth / 2, 85);

      // 显示下一级需要的修炼点
      if (this.playerSkillLevel < 30) {
        const nextLevelData = SkillConfig.getSkillLevelData(this.currentSkillId, this.playerSkillLevel + 1);
        if (nextLevelData) {
          this.ctx.fillStyle = '#90EE90';
          this.ctx.fillText(`修炼到${this.playerSkillLevel + 1}级需要: ${nextLevelData.cost}修炼点`, this.screenWidth / 2, 105);
        }
      }
    }

    // 显示玩家当前修炼点
    try {
      const player = game.gameStateManager.getPlayer();
      const functionPoints = player.resources && player.resources.functionPoints ? player.resources.functionPoints : 0;

      this.ctx.font = '14px Arial';
      this.ctx.fillStyle = '#90EE90';
      this.ctx.textAlign = 'right';
      this.ctx.fillText(`修炼点: ${functionPoints}`, this.screenWidth - 20, 30);
    } catch (error) {
      console.error('绘制修炼点失败:', error);
    }
  }

  // 绘制连接线
  drawConnections() {
    if (!this.nodePositions || this.nodePositions.length === 0) return;

    // 绘制节点之间的连接线：1->2->3->4->5->6->7->8->9->10
    this.ctx.strokeStyle = 'rgba(255, 215, 0, 0.6)';
    this.ctx.lineWidth = 3;

    // 连接相邻节点：0->1->2->3->4->5->6->7->8->9
    for (let i = 0; i < this.nodePositions.length - 1; i++) {
      const fromPos = this.nodePositions[i];
      const toPos = this.nodePositions[i + 1];

      this.ctx.beginPath();
      this.ctx.moveTo(fromPos.centerX, fromPos.centerY);
      this.ctx.lineTo(toPos.centerX, toPos.centerY);
      this.ctx.stroke();
    }
  }

  // 绘制功法信息
  drawSkillInfo() {
    if (!this.currentSkillId || this.playerSkillLevel >= 30) return;

    const skillData = SkillConfig.getSkillData(this.currentSkillId);
    const nextLevel = this.playerSkillLevel + 1;
    const nextLevelData = SkillConfig.getSkillLevelData(this.currentSkillId, nextLevel);

    if (!skillData || !nextLevelData) return;

    // 绘制下一级属性信息
    const infoY = this.screenHeight - 200;
    const infoHeight = 120;
    const margin = 20;

    // 绘制信息面板背景
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';
    this.ctx.fillRect(margin, infoY, this.screenWidth - margin * 2, infoHeight);

    // 绘制边框
    this.ctx.strokeStyle = '#FFD700';
    this.ctx.lineWidth = 2;
    this.ctx.strokeRect(margin, infoY, this.screenWidth - margin * 2, infoHeight);

    // 绘制标题
    this.ctx.font = 'bold 18px Arial';
    this.ctx.fillStyle = '#FFD700';
    this.ctx.textAlign = 'center';
    this.ctx.fillText(`修炼到第${nextLevel}层`, this.screenWidth / 2, infoY + 25);

    // 绘制属性加成
    this.ctx.font = '14px Arial';
    this.ctx.fillStyle = '#FFFFFF';
    this.ctx.textAlign = 'left';

    let yOffset = 50;
    for (const [attr, value] of Object.entries(nextLevelData.attributes)) {
      const attrName = this.getAttributeName(attr);
      this.ctx.fillText(`${attrName}: +${value}`, margin + 20, infoY + yOffset);
      yOffset += 20;
    }

    // 绘制消耗
    this.ctx.font = 'bold 16px Arial';
    this.ctx.fillStyle = '#90EE90';
    this.ctx.textAlign = 'center';
    this.ctx.fillText(`消耗修炼点: ${nextLevelData.cost}`, this.screenWidth / 2, infoY + infoHeight - 15);
  }
}

module.exports = FunctionDetailScene;