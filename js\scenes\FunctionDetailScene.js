/**
 * 功法详情场景类
 * 展示功法的节点和升级功能
 */
import BaseScene from './BaseScene';
import Button from '../ui/Button';
import game from '../../game';
import SkillConfig from '../config/SkillConfig';

class FunctionDetailScene extends BaseScene {
  constructor(ctx, screenWidth, screenHeight, sceneManager, resources) {
    super(ctx, screenWidth, screenHeight, sceneManager);

    // 场景资源
    this.resources = resources || {};

    // 当前功法信息
    this.currentSkillId = null;
    this.currentSkillName = null;
    this.currentRealm = null;

    // 功法节点数据
    this.nodes = [];

    // 节点位置信息
    this.nodePositions = [];

    // 当前玩家功法等级
    this.playerSkillLevel = 0;
  }

  // 初始化UI
  initUI() {
    // 清空UI元素
    this.clearUIElements();

    // 创建返回按钮
    const buttonWidth = 80;
    const buttonHeight = 40;
    const margin = 10;

    this.backButton = new Button(
      this.ctx,
      margin,
      margin,
      buttonWidth,
      buttonHeight,
      '返回',
      null,
      null,
      () => {
        // 返回到功法页面
        this.sceneManager.showScene('skill');
      }
    );

    this.addUIElement(this.backButton);

    // 获取玩家功法等级
    this.playerSkillLevel = this.getPlayerSkillLevel();

    // 初始化节点位置
    this.initNodePositions();

    // 创建节点按钮
    this.createNodeButtons();

    // 创建修炼按钮
    this.createUpgradeButton();
  }

  // 初始化节点位置
  initNodePositions() {
    const headerHeight = 120;
    const nodeSize = 50;

    this.nodePositions = [];

    // 根据截图的布局，创建类似的节点连线图
    // 使用类似星座图的布局
    const centerX = this.screenWidth / 2;
    const centerY = this.screenHeight / 2;
    const radius = 120;

    // 10个节点的位置，按照类似截图的布局
    const positions = [
      // 第一层 - 5个节点
      { x: centerX - radius, y: centerY - radius * 0.6 },      // 左上
      { x: centerX, y: centerY - radius },                      // 上
      { x: centerX + radius, y: centerY - radius * 0.6 },      // 右上
      { x: centerX - radius * 0.7, y: centerY },               // 左
      { x: centerX + radius * 0.7, y: centerY },               // 右

      // 第二层 - 5个节点
      { x: centerX - radius * 0.5, y: centerY + radius * 0.5 }, // 左下
      { x: centerX, y: centerY + radius * 0.8 },                // 下
      { x: centerX + radius * 0.5, y: centerY + radius * 0.5 }, // 右下
      { x: centerX - radius * 1.2, y: centerY + radius * 0.2 }, // 远左
      { x: centerX + radius * 1.2, y: centerY + radius * 0.2 }  // 远右
    ];

    positions.forEach(pos => {
      this.nodePositions.push({
        x: pos.x - nodeSize / 2,
        y: pos.y - nodeSize / 2,
        size: nodeSize,
        centerX: pos.x,
        centerY: pos.y
      });
    });
  }

  // 创建节点按钮
  createNodeButtons() {
    if (!this.currentSkillId) return;

    // 获取功法数据
    const skillData = SkillConfig.getSkillData(this.currentSkillId);
    if (!skillData) return;

    // 创建10个节点按钮
    for (let i = 0; i < 10; i++) {
      const pos = this.nodePositions[i];
      const level = i + 1;

      // 检查节点状态
      const isUnlocked = this.playerSkillLevel >= level;
      const canUnlock = this.playerSkillLevel === level - 1;
      const isNext = this.playerSkillLevel === level - 1;

      let buttonColor;
      let buttonText = level.toString();

      if (isUnlocked) {
        buttonColor = '#FFD700'; // 已点亮 - 金色
      } else if (isNext) {
        buttonColor = '#00FF00'; // 可点亮 - 绿色
      } else {
        buttonColor = 'rgba(100, 100, 100, 0.7)'; // 不可点亮 - 灰色
      }

      this.nodes[i] = new Button(
        this.ctx,
        pos.x,
        pos.y,
        pos.size,
        pos.size,
        buttonText,
        null,
        buttonColor,
        () => {
          if (isNext) {
            // 尝试升级功法
            this.upgradeSkill(level);
          } else if (isUnlocked) {
            // 显示节点属性
            this.showNodeAttributes(level);
          } else {
            // 显示提示
            console.log('需要先点亮前置节点');
          }
        }
      );

      this.addUIElement(this.nodes[i]);
    }
  }

  // 创建修炼按钮
  createUpgradeButton() {
    if (!this.currentSkillId || this.playerSkillLevel >= 10) return;

    const skillData = SkillConfig.getSkillData(this.currentSkillId);
    if (!skillData) return;

    const nextLevel = this.playerSkillLevel + 1;
    const nextLevelData = SkillConfig.getSkillLevelData(this.currentSkillId, nextLevel);

    if (nextLevelData) {
      const buttonWidth = 120;
      const buttonHeight = 50;

      this.upgradeButton = new Button(
        this.ctx,
        this.screenWidth / 2 - buttonWidth / 2,
        this.screenHeight - 120,
        buttonWidth,
        buttonHeight,
        '修炼',
        null,
        '#4CAF50',
        () => {
          this.upgradeSkill(nextLevel);
        }
      );

      this.addUIElement(this.upgradeButton);
    }
  }

  // 获取玩家功法等级
  getPlayerSkillLevel() {
    try {
      const player = game.gameStateManager.getPlayer();
      if (player && player.skills && player.skills[this.currentSkillId]) {
        return player.skills[this.currentSkillId].level || 0;
      }
      return 0;
    } catch (error) {
      console.error('获取玩家功法等级失败:', error);
      return 0;
    }
  }

  // 升级功法
  upgradeSkill(targetLevel) {
    const skillData = SkillConfig.getSkillData(this.currentSkillId);
    const levelData = SkillConfig.getSkillLevelData(this.currentSkillId, targetLevel);

    if (!skillData || !levelData) {
      console.error('功法数据不存在');
      return;
    }

    // 检查修炼点是否足够
    const player = game.gameStateManager.getPlayer();
    const functionPoints = player.resources && player.resources.functionPoints ? player.resources.functionPoints : 0;

    if (functionPoints < levelData.cost) {
      console.log(`修炼点不足，需要${levelData.cost}点，当前${functionPoints}点`);
      return;
    }

    // 扣除修炼点
    player.resources.functionPoints -= levelData.cost;

    // 更新功法等级
    if (!player.skills) player.skills = {};
    if (!player.skills[this.currentSkillId]) {
      player.skills[this.currentSkillId] = { level: 0 };
    }
    player.skills[this.currentSkillId].level = targetLevel;

    // 保存玩家数据
    game.gameStateManager.setPlayer(player);

    // 重新初始化UI
    this.playerSkillLevel = targetLevel;
    this.initUI();

    console.log(`功法${skillData.name}升级到${targetLevel}级成功`);
  }

  // 显示节点属性
  showNodeAttributes(level) {
    const skillData = SkillConfig.getSkillData(this.currentSkillId);
    const levelData = SkillConfig.getSkillLevelData(this.currentSkillId, level);

    if (!skillData || !levelData) return;

    // 显示属性信息
    let attributeText = `${skillData.name} 第${level}层属性:\n`;

    for (const [attr, value] of Object.entries(levelData.attributes)) {
      const attrName = this.getAttributeName(attr);
      attributeText += `${attrName}: +${value}\n`;
    }

    console.log(attributeText);
  }

  // 获取属性中文名称
  getAttributeName(attr) {
    const attrMap = {
      'hp': '生命值',
      'attack': '攻击力',
      'defense': '防御力',
      'speed': '速度',
      'critical': '暴击率'
    };
    return attrMap[attr] || attr;
  }

  // 场景显示时的回调
  onShow(params) {
    // 从参数中获取功法信息
    if (params) {
      this.currentSkillId = params.skillId;
      this.currentSkillName = params.skillName;
      this.currentRealm = params.realm;
    }

    // 初始化UI
    this.initUI();
  }

  // 场景隐藏时的回调
  onHide() {
    // 清空UI元素
    this.clearUIElements();
  }

  // 绘制场景
  drawScene() {
    // 绘制背景
    this.drawBackground();

    // 绘制标题
    this.drawTitle();

    // 绘制连接线
    this.drawConnections();

    // 绘制功法信息
    this.drawSkillInfo();
  }

  // 绘制背景
  drawBackground() {
    // 绘制渐变背景
    const gradient = this.ctx.createLinearGradient(0, 0, 0, this.screenHeight);
    gradient.addColorStop(0, 'rgba(20, 30, 60, 0.95)');
    gradient.addColorStop(0.5, 'rgba(40, 20, 60, 0.95)');
    gradient.addColorStop(1, 'rgba(10, 15, 30, 0.95)');
    this.ctx.fillStyle = gradient;
    this.ctx.fillRect(0, 0, this.screenWidth, this.screenHeight);
  }

  // 绘制标题
  drawTitle() {
    if (!this.currentSkillName) return;

    const headerHeight = 80;

    // 绘制功法名称
    this.ctx.font = 'bold 24px Arial';
    this.ctx.fillStyle = '#FFD700';
    this.ctx.textAlign = 'center';
    this.ctx.fillText(this.currentSkillName, this.screenWidth / 2, 40);

    // 绘制功法等级和修炼点信息
    const skillData = SkillConfig.getSkillData(this.currentSkillId);
    if (skillData) {
      this.ctx.font = '16px Arial';
      this.ctx.fillStyle = '#FFFFFF';
      this.ctx.fillText(`当前等级: ${this.playerSkillLevel}/10`, this.screenWidth / 2, 65);

      // 显示下一级需要的修炼点
      if (this.playerSkillLevel < 10) {
        const nextLevelData = SkillConfig.getSkillLevelData(this.currentSkillId, this.playerSkillLevel + 1);
        if (nextLevelData) {
          this.ctx.fillStyle = '#90EE90';
          this.ctx.fillText(`修炼到${this.playerSkillLevel + 1}级需要: ${nextLevelData.cost}修炼点`, this.screenWidth / 2, 85);
        }
      }
    }

    // 显示玩家当前修炼点
    try {
      const player = game.gameStateManager.getPlayer();
      const functionPoints = player.resources && player.resources.functionPoints ? player.resources.functionPoints : 0;

      this.ctx.font = '14px Arial';
      this.ctx.fillStyle = '#90EE90';
      this.ctx.textAlign = 'right';
      this.ctx.fillText(`修炼点: ${functionPoints}`, this.screenWidth - 20, 30);
    } catch (error) {
      console.error('绘制修炼点失败:', error);
    }
  }

  // 绘制连接线
  drawConnections() {
    if (!this.nodePositions || this.nodePositions.length === 0) return;

    // 绘制节点之间的连接线
    this.ctx.strokeStyle = 'rgba(255, 215, 0, 0.6)';
    this.ctx.lineWidth = 2;

    // 定义连接关系（类似截图的连线）
    const connections = [
      [0, 1], [1, 2], [2, 4], [4, 7], [7, 6], [6, 5], [5, 3], [3, 0], // 外圈
      [1, 3], [2, 6], [4, 5], [0, 7], // 内部连线
      [8, 3], [9, 4], [8, 5], [9, 7] // 外部节点连线
    ];

    connections.forEach(([from, to]) => {
      if (from < this.nodePositions.length && to < this.nodePositions.length) {
        const fromPos = this.nodePositions[from];
        const toPos = this.nodePositions[to];

        this.ctx.beginPath();
        this.ctx.moveTo(fromPos.centerX, fromPos.centerY);
        this.ctx.lineTo(toPos.centerX, toPos.centerY);
        this.ctx.stroke();
      }
    });
  }

  // 绘制功法信息
  drawSkillInfo() {
    if (!this.currentSkillId || this.playerSkillLevel >= 10) return;

    const skillData = SkillConfig.getSkillData(this.currentSkillId);
    const nextLevel = this.playerSkillLevel + 1;
    const nextLevelData = SkillConfig.getSkillLevelData(this.currentSkillId, nextLevel);

    if (!skillData || !nextLevelData) return;

    // 绘制下一级属性信息
    const infoY = this.screenHeight - 200;
    const infoHeight = 120;
    const margin = 20;

    // 绘制信息面板背景
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';
    this.ctx.fillRect(margin, infoY, this.screenWidth - margin * 2, infoHeight);

    // 绘制边框
    this.ctx.strokeStyle = '#FFD700';
    this.ctx.lineWidth = 2;
    this.ctx.strokeRect(margin, infoY, this.screenWidth - margin * 2, infoHeight);

    // 绘制标题
    this.ctx.font = 'bold 18px Arial';
    this.ctx.fillStyle = '#FFD700';
    this.ctx.textAlign = 'center';
    this.ctx.fillText(`修炼到第${nextLevel}层`, this.screenWidth / 2, infoY + 25);

    // 绘制属性加成
    this.ctx.font = '14px Arial';
    this.ctx.fillStyle = '#FFFFFF';
    this.ctx.textAlign = 'left';

    let yOffset = 50;
    for (const [attr, value] of Object.entries(nextLevelData.attributes)) {
      const attrName = this.getAttributeName(attr);
      this.ctx.fillText(`${attrName}: +${value}`, margin + 20, infoY + yOffset);
      yOffset += 20;
    }

    // 绘制消耗
    this.ctx.font = 'bold 16px Arial';
    this.ctx.fillStyle = '#90EE90';
    this.ctx.textAlign = 'center';
    this.ctx.fillText(`消耗修炼点: ${nextLevelData.cost}`, this.screenWidth / 2, infoY + infoHeight - 15);
  }
}

module.exports = FunctionDetailScene;