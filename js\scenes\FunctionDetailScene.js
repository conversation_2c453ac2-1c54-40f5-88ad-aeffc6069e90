/**
 * 功法详情场景类
 * 展示功法的节点和升级功能
 */
import BaseScene from './BaseScene';
import Button from '../ui/Button';
import game from '../../game';

class FunctionDetailScene extends BaseScene {
  constructor(ctx, screenWidth, screenHeight, sceneManager, resources) {
    super(ctx, screenWidth, screenHeight, sceneManager);
    
    // 场景资源
    this.resources = resources || {};
    
    // 当前功法ID
    this.currentFunctionId = null;
    
    // 功法节点数据
    this.nodes = [];
    
    // 节点位置信息
    this.nodePositions = [];
    
    // 节点属性加成定义
    this.nodeAttributes = [
      { name: '生命加成', value: 50, percent: false },
      { name: '攻击加成', value: 5, percent: false },
      { name: '防御加成', value: 3, percent: false },
      { name: '速度加成', value: 1, percent: false },
      { name: '暴击率', value: 1, percent: true },
      { name: '暴击伤害', value: 5, percent: true },
      { name: '大道法则', value: 1, percent: false },
      { name: '破防', value: 1, percent: false },
      { name: '生命百分比', value: 2, percent: true },
      // 添加额外的节点属性以支持10个节点
      { name: '灵气回复', value: 2, percent: false },
      // 第二层节点
      { name: '灵力恢复', value: 100, percent: false },
      { name: '防御百分比', value: 3, percent: true },
      { name: '攻击百分比', value: 4, percent: true },
      { name: '闪避率', value: 2, percent: true },
      { name: '命中率', value: 3, percent: true },
      { name: '法术强度', value: 5, percent: false },
      { name: '神识强度', value: 4, percent: false },
      { name: '灵气凝聚', value: 3, percent: false },
      { name: '丹田容量', value: 100, percent: false },
      { name: '元素亲和', value: 5, percent: false },
      // 第三层节点
      { name: '伤害减免', value: 3, percent: true },
      { name: '伤害反弹', value: 2, percent: true },
      { name: '法力护盾', value: 50, percent: false },
      { name: '回春能力', value: 5, percent: false },
      { name: '毒素抗性', value: 10, percent: true },
      { name: '雷电抗性', value: 10, percent: true },
      { name: '火焰抗性', value: 10, percent: true },
      { name: '寒冰抗性', value: 10, percent: true },
      { name: '气血恢复', value: 10, percent: false },
      { name: '剑气凝聚', value: 6, percent: false }
    ];
  }
  
  // 初始化UI
  initUI() {
    // 清空UI元素
    this.clearUIElements();
    
    // 创建返回按钮
    const buttonWidth = 80;
    const buttonHeight = 40;
    const margin = 10;
    
    this.backButton = new Button(
      this.ctx,
      margin,
      margin,
      buttonWidth,
      buttonHeight,
      '返回',
      null,
      null,
      () => {
        // 返回到功法页面
        this.sceneManager.showScene('function');
      }
    );
    
    this.addUIElement(this.backButton);
    
    // 初始化节点位置
    this.initNodePositions();
    
    // 创建节点按钮
    this.createNodeButtons();
  }
  
  // 初始化节点位置
  initNodePositions() {
    const headerHeight = 120;
    const nodeSize = 40; // 减小节点尺寸以适应更多节点
    const horizontalGap = (this.screenWidth - nodeSize * 5) / 6; // 每行5个节点
    const verticalGap = 60;
    
    this.nodePositions = [];
    
    // 计算2行5列，共10个节点的位置
    for (let row = 0; row < 2; row++) {
      for (let col = 0; col < 5; col++) {
        this.nodePositions.push({
          x: horizontalGap + col * (nodeSize + horizontalGap),
          y: headerHeight + row * (nodeSize + verticalGap),
          size: nodeSize
        });
      }
    }
  }
  
  // 创建节点按钮
  createNodeButtons() {
    if (!this.currentFunctionId) return;
    
    // 获取功法数据
    const functionData = this.getFunctionData(this.currentFunctionId);
    if (!functionData) return;
    
    // 获取功法解锁的节点
    const unlockedNodes = this.getUnlockedNodes(this.currentFunctionId);
    
    // 功法层数
    const level = functionData.level;
    
    // 获取当前修炼层次 (从0开始计数)
    const currentLayer = Math.floor(unlockedNodes.length / 10);
    
    // 功法节点总数，每层10个节点
    const totalNodes = this.getMaxNodeCount(level);
    const currentLayerTotalNodes = Math.min(10, totalNodes - currentLayer * 10);
    
    // 创建当前层的节点按钮
    for (let i = 0; i < currentLayerTotalNodes; i++) {
      const nodeIndex = currentLayer * 10 + i;
      const pos = this.nodePositions[i];
      
      // 检查节点解锁状态
      const isUnlocked = unlockedNodes.includes(nodeIndex);
      const canUnlock = this.canUnlockNode(nodeIndex, unlockedNodes);
      
      this.nodes[i] = new Button(
        this.ctx,
        pos.x,
        pos.y,
        pos.size,
        pos.size,
        (i + 1).toString(),
        null,
        isUnlocked ? '#4CAF50' : (canUnlock ? '#2196F3' : 'rgba(100, 100, 100, 0.7)'),
        () => {
          if (isUnlocked) {
            // 显示节点属性
            this.showNodeAttributes(nodeIndex);
          } else if (canUnlock) {
            // 尝试解锁节点
            this.unlockNode(nodeIndex);
          } else {
            wx.showToast({
              title: '需要先解锁前置节点',
              icon: 'none',
              duration: 2000
            });
          }
        }
      );
      
      this.addUIElement(this.nodes[i]);
    }
    
    // 显示当前层信息
    this.currentLayer = currentLayer + 1; // 从1开始显示
    
    // 如果当前层所有节点都已解锁，并且还有下一层，显示进入下一层的按钮
    if (this.isCurrentLayerComplete(currentLayer, unlockedNodes) && 
        (currentLayer + 1) * 10 < totalNodes) {
      
      const buttonWidth = 150;
      const buttonHeight = 40;
      const nextLayerButton = new Button(
        this.ctx,
        (this.screenWidth - buttonWidth) / 2,
        this.screenHeight * 0.7,
        buttonWidth,
        buttonHeight,
        '进入下一层',
        null,
        '#4CAF50',
        () => {
          this.advanceToNextLayer();
        }
      );
      
      this.addUIElement(nextLayerButton);
    }
  }
  
  // 检查当前层是否已全部完成
  isCurrentLayerComplete(layer, unlockedNodes) {
    const startIdx = layer * 10;
    const endIdx = startIdx + 9;
    
    for (let i = startIdx; i <= endIdx; i++) {
      if (!unlockedNodes.includes(i)) {
        return false;
      }
    }
    
    return true;
  }
  
  // 进入下一层
  advanceToNextLayer() {
    // 重新初始化UI以显示下一层
    this.initUI();
    
    // 显示提示信息
    wx.showToast({
      title: `进入第${this.currentLayer + 1}层修炼`,
      icon: 'success',
      duration: 2000
    });
  }
  
  // 获取功法数据
  getFunctionData(functionId) {
    // 解析功法ID获取等级和索引
    const parts = functionId.split('_');
    if (parts.length !== 3) return null;
    
    const level = parseInt(parts[1]);
    const index = parseInt(parts[2]) - 1;
    
    // 所有功法数据
    const allFunctions = [
      // 第一阶 - 炼气期
      {
        realm: '炼气期',
        functions: [
          { id: 'function_1_1', name: '聚气诀', level: 1, description: '炼气初期入门功法', unlockPhase: '初期' },
          { id: 'function_1_2', name: '引气诀', level: 1, description: '炼气中期修炼功法', unlockPhase: '中期' },
          { id: 'function_1_3', name: '凝气诀', level: 1, description: '炼气后期修炼功法', unlockPhase: '后期' }
        ]
      },
      // 第二阶 - 筑基期
      {
        realm: '筑基期',
        functions: [
          { id: 'function_2_1', name: '固本培元', level: 2, description: '筑基初期修炼功法', unlockPhase: '初期' },
          { id: 'function_2_2', name: '灵气归元', level: 2, description: '筑基中期修炼功法', unlockPhase: '中期' },
          { id: 'function_2_3', name: '筑基诀', level: 2, description: '筑基后期修炼功法', unlockPhase: '后期' }
        ]
      },
      // 其他功法省略...
    ];
    
    // 查找对应功法
    if (level >= 1 && level <= allFunctions.length) {
      const realmData = allFunctions[level - 1];
      if (index >= 0 && index < realmData.functions.length) {
        return realmData.functions[index];
      }
    }
    
    return null;
  }
  
  // 获取解锁的节点
  getUnlockedNodes(functionId) {
    const player = game.gameStateManager.getPlayer();
    if (!player.functions) player.functions = {};
    if (!player.functions[functionId]) player.functions[functionId] = [];
    
    return player.functions[functionId];
  }
  
  // 获取功法最大节点数
  getMaxNodeCount(level) {
    // 每个功法等级有对应数量的层，每层10个节点
    // 一阶功法有1层(10个节点)，二阶功法有2层(20个节点)，三阶及以上每阶多一层
    return level * 10;
  }
  
  // 判断节点是否可以解锁
  canUnlockNode(nodeIndex, unlockedNodes) {
    // 检查是否是当前层的第一个节点
    if (nodeIndex % 10 === 0) {
      // 如果是第一层第一个节点，可以直接解锁
      if (nodeIndex === 0) return true;
      
      // 对于其他层的第一个节点，需要前一层全部解锁
      const prevLayerStart = nodeIndex - 10;
      const prevLayerEnd = nodeIndex - 1;
      
      for (let i = prevLayerStart; i <= prevLayerEnd; i++) {
        if (!unlockedNodes.includes(i)) {
          return false;
        }
      }
      
      return true;
    }
    
    // 对于同层的其他节点，需要前一个节点解锁
    return unlockedNodes.includes(nodeIndex - 1);
  }
  
  // 获取节点的前置节点
  getNodePrerequisites(nodeIndex) {
    // 新的前置节点逻辑
    if (nodeIndex % 10 === 0) {
      // 是某层的第一个节点
      if (nodeIndex === 0) return []; // 第一层第一个节点没有前置条件
      
      // 返回前一层所有节点作为前置条件
      const prerequisites = [];
      for (let i = nodeIndex - 10; i < nodeIndex; i++) {
        prerequisites.push(i);
      }
      return prerequisites;
    } else {
      // 同层其他节点，只需前一个节点
      return [nodeIndex - 1];
    }
  }
  
  // 显示节点属性
  showNodeAttributes(nodeIndex) {
    // 获取节点属性
    const attribute = this.nodeAttributes[nodeIndex];
    
    wx.showModal({
      title: `节点${nodeIndex + 1}属性`,
      content: `${attribute.name}: +${attribute.value}${attribute.percent ? '%' : ''}`,
      showCancel: false
    });
  }
  
  // 解锁节点
  unlockNode(nodeIndex) {
    const player = game.gameStateManager.getPlayer();
    const resources = player.resources || {};
    const functionPoints = resources.functionPoints || 0;
    
    // 计算解锁所需的修炼点
    const cost = this.getUnlockCost(nodeIndex);
    
    if (functionPoints < cost) {
      wx.showToast({
        title: `功法修炼点不足，需要${cost}点`,
        icon: 'none',
        duration: 2000
      });
      return;
    }
    
    // 确认解锁
    wx.showModal({
      title: '解锁节点',
      content: `消耗${cost}功法修炼点解锁节点${nodeIndex + 1}，获得${this.nodeAttributes[nodeIndex].name}+${this.nodeAttributes[nodeIndex].value}${this.nodeAttributes[nodeIndex].percent ? '%' : ''}`,
      success: (res) => {
        if (res.confirm) {
          // 扣除修炼点
          resources.functionPoints -= cost;
          
          // 初始化功法数据结构
          if (!player.functions) player.functions = {};
          if (!player.functions[this.currentFunctionId]) {
            player.functions[this.currentFunctionId] = [];
          }
          
          // 添加解锁节点
          player.functions[this.currentFunctionId].push(nodeIndex);
          
          // 更新角色属性
          this.updateCharacterAttributes(nodeIndex);
          
          // 更新玩家数据
          game.gameStateManager.setPlayer(player);
          
          // 重新绘制UI
          this.initUI();
          
          wx.showToast({
            title: '节点解锁成功',
            icon: 'success',
            duration: 2000
          });
        }
      }
    });
  }
  
  // 获取解锁节点所需的修炼点
  getUnlockCost(nodeIndex) {
    // 解析功法ID获取等级
    const parts = this.currentFunctionId.split('_');
    const level = parseInt(parts[1]);
    
    // 基础解锁成本
    const baseCost = 10 * level;
    
    // 节点位置加成
    const positionMultiplier = 1 + nodeIndex * 0.5;
    
    return Math.floor(baseCost * positionMultiplier);
  }
  
  // 更新角色属性
  updateCharacterAttributes(nodeIndex) {
    const attribute = this.nodeAttributes[nodeIndex];
    const characters = game.gameStateManager.getCharacters();
    
    // 解析功法ID获取等级
    const parts = this.currentFunctionId.split('_');
    const level = parseInt(parts[1]);
    
    // 属性增益随功法等级提高
    const attrValue = attribute.value * Math.pow(1.5, level - 1);
    
    // 更新所有角色的属性
    characters.forEach(character => {
      if (attribute.percent) {
        // 百分比加成
        if (attribute.name === '暴击率') {
          character.attributes.critRate += attrValue / 100;
        } else if (attribute.name === '暴击伤害') {
          character.attributes.critDamage += attrValue / 100;
        } else if (attribute.name === '生命百分比') {
          character.attributes.hpBonus = (character.attributes.hpBonus || 0) + attrValue / 100;
        }
      } else {
        // 固定值加成
        if (attribute.name === '生命加成') {
          character.attributes.hp += attrValue;
        } else if (attribute.name === '攻击加成') {
          character.attributes.attack += attrValue;
        } else if (attribute.name === '防御加成') {
          character.attributes.defense += attrValue;
        } else if (attribute.name === '速度加成') {
          character.attributes.speed += attrValue;
        } else if (attribute.name === '大道法则') {
          character.attributes.daoRule = (character.attributes.daoRule || 0) + attrValue;
        } else if (attribute.name === '破防') {
          character.attributes.penetration = (character.attributes.penetration || 0) + attrValue;
        }
      }
      
      // 更新角色属性
      game.gameStateManager.updateCharacter(character.id, character);
    });
  }
  
  // 场景显示时的回调
  onShow(params) {
    // 从参数中获取功法ID
    if (params && params.functionId) {
      this.currentFunctionId = params.functionId;
    }
    
    // 初始化UI
    this.initUI();
  }
  
  // 场景隐藏时的回调
  onHide() {
    // 清空UI元素
    this.clearUIElements();
  }
  
  // 绘制场景
  drawScene() {
    // 绘制背景
    this.drawBackground();
    
    // 绘制标题
    this.drawTitle();
    
    // 绘制连接线
    this.drawConnections();
  }
  
  // 绘制背景
  drawBackground() {
    const gradient = this.ctx.createLinearGradient(0, 0, 0, this.screenHeight);
    gradient.addColorStop(0, '#1a2a6c');
    gradient.addColorStop(0.5, '#b21f1f'); 
    gradient.addColorStop(1, '#fdbb2d');
    
    this.ctx.fillStyle = gradient;
    this.ctx.fillRect(0, 0, this.screenWidth, this.screenHeight);
  }
  
  // 绘制标题
  drawTitle() {
    const headerHeight = 60;
    
    // 绘制标题栏背景
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
    this.ctx.fillRect(0, 0, this.screenWidth, headerHeight);
    
    // 绘制功法名称
    if (this.currentFunctionId) {
      const functionData = this.getFunctionData(this.currentFunctionId);
      if (functionData) {
        this.ctx.fillStyle = '#FFFFFF';
        this.ctx.font = 'bold 24px Arial';
        this.ctx.textAlign = 'center';
        this.ctx.fillText(functionData.name, this.screenWidth / 2, headerHeight / 2 + 8);
      }
    }
    
    // 绘制功法修炼点
    const player = game.gameStateManager.getPlayer();
    const functionPoints = player.resources && player.resources.functionPoints ? player.resources.functionPoints : 0;
    
    this.ctx.font = '16px Arial';
    this.ctx.textAlign = 'right';
    this.ctx.fillText(`修炼点: ${functionPoints}`, this.screenWidth - 20, headerHeight / 2 + 8);
    
    // 绘制功法描述
    if (this.currentFunctionId) {
      const functionData = this.getFunctionData(this.currentFunctionId);
      if (functionData) {
        this.ctx.font = '16px Arial';
        this.ctx.textAlign = 'center';
        this.ctx.fillText(functionData.description, this.screenWidth / 2, headerHeight + 20);
      }
    }
  }
  
  // 绘制节点连接线
  drawConnections() {
    // 获取解锁的节点
    const unlockedNodes = this.getUnlockedNodes(this.currentFunctionId);
    
    // 功法数据
    const functionData = this.getFunctionData(this.currentFunctionId);
    if (!functionData) return;
    
    // 计算当前层
    const currentLayer = Math.floor(unlockedNodes.length / 10);
    
    // 绘制连接线
    this.ctx.lineWidth = 2;
    
    // 处理当前层内部的连接
    for (let i = 0; i < 9; i++) {
      const nodeIndex = currentLayer * 10 + i;
      const nextNodeIndex = nodeIndex + 1;
      
      // 获取节点位置
      const pos = this.nodePositions[i];
      const nextPos = this.nodePositions[i + 1];
      
      // 检查节点解锁状态
      const isUnlocked = unlockedNodes.includes(nodeIndex);
      const nextIsUnlocked = unlockedNodes.includes(nextNodeIndex);
      
      // 设置连接线颜色
      if (isUnlocked && nextIsUnlocked) {
        this.ctx.strokeStyle = '#4CAF50'; // 都解锁了，绿色
      } else if (isUnlocked) {
        this.ctx.strokeStyle = '#2196F3'; // 起点解锁，终点未解锁，蓝色
      } else {
        this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)'; // 都未解锁，灰色
      }
      
      // 绘制连接线
      this.ctx.beginPath();
      this.ctx.moveTo(pos.x + pos.size / 2, pos.y + pos.size / 2);
      this.ctx.lineTo(nextPos.x + nextPos.size / 2, nextPos.y + nextPos.size / 2);
      this.ctx.stroke();
    }
    
    // 绘制层次信息
    this.ctx.fillStyle = '#FFD700';
    this.ctx.font = 'bold 20px Arial';
    this.ctx.textAlign = 'center';
    this.ctx.fillText(`第${this.currentLayer}层修炼`, this.screenWidth / 2, this.screenHeight * 0.6);
  }
}

// 修改导出方式为CommonJS
module.exports = FunctionDetailScene; 