/**
 * 功法配置文件
 * 定义所有功法的数据结构和属性
 */

const SkillConfig = {
  // 功法数据按境界分组
  skills: {
    // 练气期功法
    '练气期': {
      '练气决': {
        id: 'lianqi_jue',
        name: '练气决',
        realm: '练气期',
        description: '修仙者入门功法，通过吸收天地灵气来提升修为',
        icon: 'lianqi_jue.png',
        levels: [
          { level: 1, attributes: { attack: 10 }, cost: 10 },
          { level: 2, attributes: { hp: 50 }, cost: 15 },
          { level: 3, attributes: { attack: 15, defense: 5 }, cost: 20 },
          { level: 4, attributes: { hp: 80, attack: 10 }, cost: 25 },
          { level: 5, attributes: { defense: 15, speed: 5 }, cost: 30 },
          { level: 6, attributes: { attack: 20, hp: 100 }, cost: 35 },
          { level: 7, attributes: { defense: 20, attack: 15 }, cost: 40 },
          { level: 8, attributes: { hp: 150, speed: 10 }, cost: 45 },
          { level: 9, attributes: { attack: 25, defense: 25 }, cost: 50 },
          { level: 10, attributes: { hp: 200, attack: 30, defense: 30 }, cost: 60 }
        ]
      },
      '基础剑法': {
        id: 'basic_sword',
        name: '基础剑法',
        realm: '练气期',
        description: '最基础的剑法修炼，提升剑术造诣',
        icon: 'basic_sword.png',
        levels: [
          { level: 1, attributes: { attack: 15 }, cost: 12 },
          { level: 2, attributes: { attack: 20, critical: 2 }, cost: 18 },
          { level: 3, attributes: { attack: 25, speed: 5 }, cost: 24 },
          { level: 4, attributes: { attack: 30, critical: 5 }, cost: 30 },
          { level: 5, attributes: { attack: 35, speed: 8, critical: 8 }, cost: 36 },
          { level: 6, attributes: { attack: 40, critical: 10 }, cost: 42 },
          { level: 7, attributes: { attack: 45, speed: 12 }, cost: 48 },
          { level: 8, attributes: { attack: 50, critical: 15 }, cost: 54 },
          { level: 9, attributes: { attack: 55, speed: 15, critical: 18 }, cost: 60 },
          { level: 10, attributes: { attack: 60, critical: 20, speed: 20 }, cost: 70 }
        ]
      },
      '金刚体': {
        id: 'jingang_ti',
        name: '金刚体',
        realm: '练气期',
        description: '炼体功法，强化肉身防御',
        icon: 'jingang_ti.png',
        levels: [
          { level: 1, attributes: { defense: 15 }, cost: 12 },
          { level: 2, attributes: { hp: 80, defense: 10 }, cost: 18 },
          { level: 3, attributes: { defense: 25, hp: 60 }, cost: 24 },
          { level: 4, attributes: { defense: 30, hp: 100 }, cost: 30 },
          { level: 5, attributes: { defense: 35, hp: 120 }, cost: 36 },
          { level: 6, attributes: { defense: 40, hp: 150 }, cost: 42 },
          { level: 7, attributes: { defense: 45, hp: 180 }, cost: 48 },
          { level: 8, attributes: { defense: 50, hp: 200 }, cost: 54 },
          { level: 9, attributes: { defense: 55, hp: 250 }, cost: 60 },
          { level: 10, attributes: { defense: 60, hp: 300 }, cost: 70 }
        ]
      }
    },

    // 筑基期功法
    '筑基期': {
      '筑基心法': {
        id: 'zhuji_xinfa',
        name: '筑基心法',
        realm: '筑基期',
        description: '筑基期修炼心法，凝聚灵力筑基',
        icon: 'zhuji_xinfa.png',
        levels: [
          { level: 1, attributes: { hp: 200, attack: 30 }, cost: 50 },
          { level: 2, attributes: { hp: 150, defense: 25 }, cost: 60 },
          { level: 3, attributes: { attack: 40, speed: 15 }, cost: 70 },
          { level: 4, attributes: { hp: 250, attack: 35, defense: 20 }, cost: 80 },
          { level: 5, attributes: { attack: 50, critical: 15 }, cost: 90 },
          { level: 6, attributes: { hp: 300, defense: 35 }, cost: 100 },
          { level: 7, attributes: { attack: 60, speed: 25 }, cost: 110 },
          { level: 8, attributes: { hp: 350, attack: 45, defense: 30 }, cost: 120 },
          { level: 9, attributes: { attack: 70, critical: 25, speed: 20 }, cost: 130 },
          { level: 10, attributes: { hp: 400, attack: 80, defense: 50, critical: 30 }, cost: 150 }
        ]
      },
      '御剑术': {
        id: 'yujian_shu',
        name: '御剑术',
        realm: '筑基期',
        description: '以神识御剑，剑气纵横',
        icon: 'yujian_shu.png',
        levels: [
          { level: 1, attributes: { attack: 50, critical: 10 }, cost: 55 },
          { level: 2, attributes: { attack: 40, speed: 20 }, cost: 65 },
          { level: 3, attributes: { attack: 60, critical: 15 }, cost: 75 },
          { level: 4, attributes: { attack: 55, speed: 25, critical: 12 }, cost: 85 },
          { level: 5, attributes: { attack: 70, critical: 20 }, cost: 95 },
          { level: 6, attributes: { attack: 65, speed: 30 }, cost: 105 },
          { level: 7, attributes: { attack: 80, critical: 25 }, cost: 115 },
          { level: 8, attributes: { attack: 75, speed: 35, critical: 20 }, cost: 125 },
          { level: 9, attributes: { attack: 90, critical: 30, speed: 25 }, cost: 135 },
          { level: 10, attributes: { attack: 100, critical: 40, speed: 40 }, cost: 160 }
        ]
      },
      '灵盾术': {
        id: 'lingdun_shu',
        name: '灵盾术',
        realm: '筑基期',
        description: '凝聚灵力护盾，抵御攻击',
        icon: 'lingdun_shu.png',
        levels: [
          { level: 1, attributes: { defense: 40, hp: 150 }, cost: 55 },
          { level: 2, attributes: { defense: 35, hp: 200 }, cost: 65 },
          { level: 3, attributes: { defense: 50, hp: 180 }, cost: 75 },
          { level: 4, attributes: { defense: 45, hp: 250 }, cost: 85 },
          { level: 5, attributes: { defense: 60, hp: 220 }, cost: 95 },
          { level: 6, attributes: { defense: 55, hp: 300 }, cost: 105 },
          { level: 7, attributes: { defense: 70, hp: 280 }, cost: 115 },
          { level: 8, attributes: { defense: 65, hp: 350 }, cost: 125 },
          { level: 9, attributes: { defense: 80, hp: 320 }, cost: 135 },
          { level: 10, attributes: { defense: 90, hp: 400 }, cost: 160 }
        ]
      }
    },

    // 金丹期功法
    '金丹期': {
      '金丹真诀': {
        id: 'jindan_zhenjue',
        name: '金丹真诀',
        realm: '金丹期',
        description: '金丹期核心功法，凝聚金丹',
        icon: 'jindan_zhenjue.png',
        levels: [
          { level: 1, attributes: { hp: 500, attack: 80, defense: 60 }, cost: 100 },
          { level: 2, attributes: { hp: 400, attack: 100, critical: 20 }, cost: 120 },
          { level: 3, attributes: { attack: 90, defense: 70, speed: 30 }, cost: 140 },
          { level: 4, attributes: { hp: 600, attack: 110, defense: 50 }, cost: 160 },
          { level: 5, attributes: { attack: 120, critical: 35, speed: 25 }, cost: 180 },
          { level: 6, attributes: { hp: 700, defense: 80, critical: 25 }, cost: 200 },
          { level: 7, attributes: { attack: 140, speed: 40, critical: 30 }, cost: 220 },
          { level: 8, attributes: { hp: 800, attack: 130, defense: 90 }, cost: 240 },
          { level: 9, attributes: { attack: 160, critical: 45, speed: 35 }, cost: 260 },
          { level: 10, attributes: { hp: 1000, attack: 180, defense: 120, critical: 50 }, cost: 300 }
        ]
      }
    }
  },

  // 境界顺序
  realmOrder: ['练气期', '筑基期', '金丹期', '元婴期', '化神期', '返虚期', '合道期', '渡劫期', '大乘期'],

  // 获取指定境界的功法列表
  getSkillsByRealm(realm) {
    return this.skills[realm] || {};
  },

  // 获取所有功法数据
  getAllSkills() {
    const allSkills = {};
    for (const realm of this.realmOrder) {
      if (this.skills[realm]) {
        Object.assign(allSkills, this.skills[realm]);
      }
    }
    return allSkills;
  },

  // 获取功法数据
  getSkillData(skillId) {
    for (const realm of this.realmOrder) {
      if (this.skills[realm]) {
        for (const skillName in this.skills[realm]) {
          const skill = this.skills[realm][skillName];
          if (skill.id === skillId) {
            return skill;
          }
        }
      }
    }
    return null;
  },

  // 获取功法指定等级的数据
  getSkillLevelData(skillId, level) {
    const skillData = this.getSkillData(skillId);
    if (!skillData || !skillData.levels) return null;
    
    return skillData.levels.find(l => l.level === level) || null;
  }
};

export default SkillConfig;
