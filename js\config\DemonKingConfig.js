/**
 * 妖王挑战配置文件
 * 定义各境界妖王的属性和奖励数据
 */

const DemonKingConfig = {
  // 妖王数据按境界分组
  demonKings: {
    '练气期': {
      id: 'lianqi_demon_king',
      name: '练气期妖王',
      realm: '练气期',
      description: '盘踞在青山之中的妖王，实力相当于练气期巅峰修士',
      level: 12,
      attributes: {
        hp: 2000,
        attack: 150,
        defense: 80,
        speed: 60,
        critical: 10
      },
      skills: [
        {
          id: 'demon_claw',
          name: '妖爪撕裂',
          description: '妖王用锋利的爪子撕裂敌人',
          damage: 180,
          cooldown: 2
        },
        {
          id: 'demon_roar',
          name: '妖王咆哮',
          description: '震慑敌人，降低其攻击力',
          effect: 'reduce_attack',
          value: 20,
          duration: 3
        }
      ],
      rewards: {
        exp: 500,
        lingshi: 100,
        functionPoints: 50,
        items: [
          { id: 'demon_core', name: '妖核', count: 1, probability: 0.3 },
          { id: 'demon_blood', name: '妖血', count: 3, probability: 0.6 },
          { id: 'spirit_herb', name: '灵草', count: 2, probability: 0.8 }
        ]
      }
    },
    
    '筑基期': {
      id: 'zhuji_demon_king',
      name: '筑基期妖王',
      realm: '筑基期',
      description: '修炼千年的妖王，已凝聚妖丹，实力恐怖',
      level: 25,
      attributes: {
        hp: 5000,
        attack: 300,
        defense: 150,
        speed: 80,
        critical: 15
      },
      skills: [
        {
          id: 'demon_fire',
          name: '妖火焚天',
          description: '释放妖火攻击敌人',
          damage: 350,
          cooldown: 3
        },
        {
          id: 'demon_shield',
          name: '妖气护盾',
          description: '用妖气形成护盾，提升防御力',
          effect: 'increase_defense',
          value: 50,
          duration: 5
        },
        {
          id: 'demon_rage',
          name: '妖王狂怒',
          description: '进入狂怒状态，攻击力大幅提升',
          effect: 'increase_attack',
          value: 100,
          duration: 4
        }
      ],
      rewards: {
        exp: 1200,
        lingshi: 250,
        functionPoints: 120,
        items: [
          { id: 'demon_pill', name: '妖丹', count: 1, probability: 0.2 },
          { id: 'demon_core', name: '妖核', count: 2, probability: 0.5 },
          { id: 'high_spirit_herb', name: '高级灵草', count: 3, probability: 0.7 },
          { id: 'demon_essence', name: '妖精', count: 1, probability: 0.4 }
        ]
      }
    },
    
    '金丹期': {
      id: 'jindan_demon_king',
      name: '金丹期妖王',
      realm: '金丹期',
      description: '妖族中的王者，拥有金丹级别的恐怖实力',
      level: 40,
      attributes: {
        hp: 12000,
        attack: 600,
        defense: 300,
        speed: 100,
        critical: 20
      },
      skills: [
        {
          id: 'demon_storm',
          name: '妖风暴',
          description: '召唤妖风暴攻击所有敌人',
          damage: 500,
          cooldown: 4,
          aoe: true
        },
        {
          id: 'demon_regeneration',
          name: '妖王再生',
          description: '快速恢复生命值',
          effect: 'heal',
          value: 2000,
          cooldown: 6
        },
        {
          id: 'demon_domain',
          name: '妖王领域',
          description: '展开妖王领域，压制敌人',
          effect: 'debuff_all',
          value: 30,
          duration: 5
        }
      ],
      rewards: {
        exp: 3000,
        lingshi: 500,
        functionPoints: 300,
        items: [
          { id: 'demon_king_core', name: '妖王核心', count: 1, probability: 0.15 },
          { id: 'demon_pill', name: '妖丹', count: 2, probability: 0.4 },
          { id: 'rare_spirit_herb', name: '珍稀灵草', count: 5, probability: 0.6 },
          { id: 'demon_soul', name: '妖魂', count: 1, probability: 0.3 }
        ]
      }
    }
  },

  // 境界顺序
  realmOrder: ['练气期', '筑基期', '金丹期', '元婴期', '化神期'],

  // 获取指定境界的妖王数据
  getDemonKingByRealm(realm) {
    return this.demonKings[realm] || null;
  },

  // 获取所有妖王数据
  getAllDemonKings() {
    return this.demonKings;
  },

  // 检查玩家是否可以挑战指定境界的妖王
  canChallenge(playerRealm, targetRealm) {
    const playerIndex = this.realmOrder.indexOf(playerRealm);
    const targetIndex = this.realmOrder.indexOf(targetRealm);
    
    // 玩家可以挑战同境界或低一个境界的妖王
    return targetIndex <= playerIndex;
  }
};

module.exports = DemonKingConfig;
