/**
 * 功法界面场景类
 * 展示所有功法，允许管理功法
 */
import BaseScene from './BaseScene';
import Button from '../ui/Button';
import game from '../../game';
import Skill from '../models/Skill';
import AppContext from '../utils/AppContext';

class SkillScene extends BaseScene {
  constructor(ctx, screenWidth, screenHeight, sceneManager) {
    super(ctx, screenWidth, screenHeight, sceneManager);
    
    // 场景资源
    this.resources = null;
    
    // 功法列表
    this.skills = [];
    
    // 当前选中的功法
    this.selectedSkillIndex = -1;
    
    // 页面状态
    this.pageTitle = '功法';
    
    // 分页
    this.itemsPerPage = 5;
    this.currentPage = 0;
  }
  
  // 初始化UI
  initUI() {
    // 尝试获取资源
    this.resources = game.resourceLoader ? game.resourceLoader.resources : null;
    
    const margin = 20;
    
    // 创建返回按钮 - 放在右下角
    const backButtonSize = 50;
    
    this.backButton = new Button(
      this.ctx,
      this.screenWidth - backButtonSize - margin,
      this.screenHeight - backButtonSize - margin,
      backButtonSize,
      backButtonSize,
      '返回',
      null,
      null,
      () => {
        // 返回主场景
        this.sceneManager.showScene('main');
      }
    );
    
    this.addUIElement(this.backButton);
    
    // 创建功法管理按钮 - 放在上部
    const actionButtonWidth = 120;
    const actionButtonHeight = 50;
    const headerHeight = 80;
    const actionButtonY = headerHeight + 10;
    
    // 装备按钮
    this.equipButton = new Button(
      this.ctx,
      this.screenWidth / 2 - actionButtonWidth - margin,
      actionButtonY,
      actionButtonWidth,
      actionButtonHeight,
      '装备功法',
      null,
      null,
      () => {
        this.equipSelectedSkill();
      }
    );
    
    this.addUIElement(this.equipButton);
    
    // 升级按钮
    this.upgradeButton = new Button(
      this.ctx,
      this.screenWidth / 2 + margin,
      actionButtonY,
      actionButtonWidth,
      actionButtonHeight,
      '升级功法',
      null,
      null,
      () => {
        this.upgradeSelectedSkill();
      }
    );
    
    this.addUIElement(this.upgradeButton);
    
    // 创建翻页按钮 - 放在中间位置
    const pageButtonWidth = 100;
    const pageButtonHeight = 40;
    const pageButtonY = this.screenHeight - 150; // 比返回按钮高一些
    
    // 上一页
    this.prevPageButton = new Button(
      this.ctx,
      margin,
      pageButtonY,
      pageButtonWidth,
      pageButtonHeight,
      '上一页',
      null,
      null,
      () => {
        if (this.currentPage > 0) {
          this.currentPage--;
        }
      }
    );
    
    this.addUIElement(this.prevPageButton);
    
    // 下一页
    this.nextPageButton = new Button(
      this.ctx,
      this.screenWidth - pageButtonWidth - margin,
      pageButtonY,
      pageButtonWidth,
      pageButtonHeight,
      '下一页',
      null,
      null,
      () => {
        const maxPage = Math.ceil(this.skills.length / this.itemsPerPage) - 1;
        if (this.currentPage < maxPage) {
          this.currentPage++;
        }
      }
    );
    
    this.addUIElement(this.nextPageButton);
  }
  
  // 场景显示时的回调
  onShow(params) {
    try {
      console.log("SkillScene onShow", params);
      this.visible = true;
      
      // 保存传入的参数，可能包含character等信息
      this.params = params || {};
      
      // 尝试从游戏上下文中获取skillManager实例
      if (AppContext && AppContext.game && AppContext.game.skillManager) {
        this.skills = AppContext.game.skillManager.getAllSkills();
        this.materials = AppContext.game.skillManager.getMaterials();
        
        console.log(`成功加载功法: ${this.skills ? this.skills.length : 0}个`);
      } else {
        console.warn("skillManager未初始化或不可用");
        // 设置为空数组避免报错
        this.skills = [];
        this.materials = {};
      }
      
      // 初始化UI状态
      this.currentPage = 0;
      this.selectedSkillIndex = -1;
      this.showingDetail = false;
      this.itemsPerPage = 4; // 每页显示的功法数量
      
      // 初始化按钮
      this.initUI();
    } catch (error) {
      console.error("SkillScene.onShow 出错:", error);
      // 设置为空数组避免报错
      this.skills = [];
      this.materials = {};
    }
  }
  
  // 场景隐藏时的回调
  onHide() {
    // 清空UI元素
    this.clearUIElements();
    
    // 设置场景为不可见
    this.visible = false;
  }
  
  // 处理触摸开始事件
  handleTouchStart(x, y) {
    const headerHeight = 80;
    const skillY = headerHeight + 20;
    const skillHeight = 100;
    const margin = 10;
    
    // 检查是否点击了功法列表
    const startIndex = this.currentPage * this.itemsPerPage;
    const endIndex = Math.min(startIndex + this.itemsPerPage, this.skills.length);
    
    for (let i = startIndex; i < endIndex; i++) {
      const relativeIndex = i - startIndex;
      const itemY = skillY + relativeIndex * (skillHeight + margin);
      
      if (y >= itemY && y <= itemY + skillHeight && 
          x >= margin && x <= this.screenWidth - margin) {
        this.selectedSkillIndex = i;
        return true;
      }
    }
    
    return false;
  }
  
  // 处理触摸移动事件
  handleTouchMove(x, y) {
    return false;
  }
  
  // 处理触摸结束事件
  handleTouchEnd(x, y) {
    return false;
  }
  
  // 装备选中的功法
  equipSelectedSkill() {
    try {
      if (this.selectedSkillIndex < 0 || this.selectedSkillIndex >= this.skills.length) {
        console.log('未选中功法');
        return;
      }
      
      // 获取选中的功法
      const selectedSkill = this.skills[this.selectedSkillIndex];
      console.log('尝试装备功法:', selectedSkill.name);
      
      // 检查是否有角色参数（从角色详情页过来的）
      if (this.params && this.params.character) {
        const character = this.params.character;
        
        // 直接装备到指定角色
        if (character.equipSkill(selectedSkill.id)) {
          console.log(`成功将功法${selectedSkill.name}装备到角色${character.name}`);
          
          // 保存游戏状态
          if (AppContext && AppContext.game && AppContext.game.gameStateManager) {
            AppContext.game.gameStateManager.updateCharacter(character.id, character);
            AppContext.game.gameStateManager.saveGameState();
          }
          
          // 返回角色详情页
          if (this.params.returnScene === 'characterDetail') {
            this.sceneManager.showScene('characterDetail', { characterId: character.id });
          } else {
            this.sceneManager.showScene('character');
          }
        } else {
          console.error('装备功法失败');
        }
      } else {
        // 无角色参数，打开角色选择场景
        console.log('打开角色选择场景以装备功法');
        this.sceneManager.showScene('character', { 
          returnScene: 'skill', 
          equipSkill: selectedSkill,
          equipSkillId: selectedSkill.id 
        });
      }
    } catch (error) {
      console.error('装备功法时出错:', error);
    }
  }
  
  // 升级选中的功法
  upgradeSelectedSkill() {
    if (this.selectedSkillIndex < 0 || this.selectedSkillIndex >= this.skills.length) {
      console.log('未选中功法');
      return;
    }
    
    // 打开功法升级场景
    const selectedSkill = this.skills[this.selectedSkillIndex];
    this.sceneManager.showScene('skillUpgrade', { skill: selectedSkill });
  }
  
  // 子类实现的绘制逻辑
  drawScene() {
    // 绘制半透明背景
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';
    this.ctx.fillRect(0, 0, this.screenWidth, this.screenHeight);
    
    // 绘制顶部导航栏
    this.drawHeader();
    
    // 绘制功法列表
    this.drawSkillList();
    
    // 绘制功法详情
    this.drawSkillDetails();
  }
  
  // 绘制顶部导航栏
  drawHeader() {
    const headerHeight = 80;
    
    // 绘制顶部导航栏背景
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
    this.ctx.fillRect(0, 0, this.screenWidth, headerHeight);
    
    // 绘制页面标题
    this.ctx.font = 'bold 24px Arial';
    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'left';
    this.ctx.fillText(this.pageTitle, 20, headerHeight / 2 + 8);
    
    // 获取skillManager并安全地访问材料信息
    const skillManager = AppContext.game.skillManager;
    if (!skillManager) {
      return;
    }
    
    const materials = skillManager.getMaterials();
    const fragments = skillManager.getFragments();
    
    if (materials && materials.skill_essence) {
      this.ctx.font = '16px Arial';
      this.ctx.fillStyle = '#ffcc00';
      this.ctx.textAlign = 'right';
      this.ctx.fillText(
        `心法要义: ${materials.skill_essence.count}`,
        this.screenWidth - 20,
        headerHeight / 2 - 10
      );
    }
    
    if (fragments) {
      this.ctx.font = '14px Arial';
      this.ctx.fillStyle = '#ffffff';
      this.ctx.textAlign = 'right';
      this.ctx.fillText(
        `功法碎片: ${Object.values(fragments).reduce((sum, f) => sum + f.count, 0)}`,
        this.screenWidth - 20,
        headerHeight / 2 + 15
      );
    }
  }
  
  // 绘制功法列表
  drawSkillList() {
    const headerHeight = 80;
    const skillY = headerHeight + 20;
    const skillHeight = 100;
    const margin = 10;
    
    // 计算当前页的功法
    const startIndex = this.currentPage * this.itemsPerPage;
    const endIndex = Math.min(startIndex + this.itemsPerPage, this.skills ? this.skills.length : 0);
    
    // 没有功法时显示提示
    if (!this.skills || !this.skills.length) {
      this.ctx.font = '18px Arial';
      this.ctx.fillStyle = '#ffffff';
      this.ctx.textAlign = 'center';
      this.ctx.fillText('暂无功法', this.screenWidth / 2, skillY + 50);
      return;
    }
    
    // 绘制功法项
    for (let i = startIndex; i < endIndex; i++) {
      const skill = this.skills[i];
      if (!skill) continue;
      
      const relativeIndex = i - startIndex;
      const itemY = skillY + relativeIndex * (skillHeight + margin);
      
      // 绘制功法项背景
      this.ctx.fillStyle = i === this.selectedSkillIndex ? 'rgba(60, 100, 150, 0.7)' : 'rgba(50, 50, 50, 0.7)';
      this.ctx.fillRect(margin, itemY, this.screenWidth - margin * 2, skillHeight);
      
      try {
        // 绘制功法边框
        this.ctx.strokeStyle = skill.getQualityColor ? skill.getQualityColor() : '#ffffff';
        this.ctx.lineWidth = 2;
        this.ctx.strokeRect(margin, itemY, this.screenWidth - margin * 2, skillHeight);
        
        // 绘制功法名称
        this.ctx.font = 'bold 18px Arial';
        this.ctx.fillStyle = skill.getQualityColor ? skill.getQualityColor() : '#ffffff';
        this.ctx.textAlign = 'left';
        this.ctx.fillText(`${skill.name}`, margin + 10, itemY + 25);
        
        // 绘制功法品质和等级
        this.ctx.font = '14px Arial';
        this.ctx.fillStyle = '#ffffff';
        this.ctx.fillText(
          `品质: ${skill.getQualityName ? skill.getQualityName() : '未知'} | 等级: ${skill.level || 1} | 星级: ${skill.stars || 1}星`,
          margin + 10,
          itemY + 50
        );
        
        // 绘制功法基本属性
        let attributeText = '';
        if (skill.attributes) {
          if (skill.attributes.hp) attributeText += `生命+${skill.attributes.hp} `;
          if (skill.attributes.hpPercent) attributeText += `生命+${skill.attributes.hpPercent}% `;
          if (skill.attributes.attack) attributeText += `攻击+${skill.attributes.attack} `;
          if (skill.attributes.attackPercent) attributeText += `攻击+${skill.attributes.attackPercent}% `;
          if (skill.attributes.defense) attributeText += `防御+${skill.attributes.defense} `;
          if (skill.attributes.defensePercent) attributeText += `防御+${skill.attributes.defensePercent}% `;
        }
        
        this.ctx.font = '12px Arial';
        this.ctx.fillStyle = '#cccccc';
        this.ctx.fillText(attributeText, margin + 10, itemY + 75);
        
        // 绘制功法战力
        this.ctx.font = 'bold 14px Arial';
        this.ctx.fillStyle = '#ffcc00';
        this.ctx.textAlign = 'right';
        this.ctx.fillText(`战力: ${skill.power || 0}`, this.screenWidth - margin - 10, itemY + 25);
        
        // 如果有限制条件，显示限制
        if (skill.restriction && skill.restriction.minLevel) {
          this.ctx.font = '12px Arial';
          this.ctx.fillStyle = '#ff6666';
          this.ctx.textAlign = 'right';
          this.ctx.fillText(
            `需求等级: ${skill.restriction.minLevel}`,
            this.screenWidth - margin - 10,
            itemY + 75
          );
        }
      } catch (error) {
        console.error('绘制功法项时出错:', error);
        // 出错时简单显示功法名称
        this.ctx.font = '16px Arial';
        this.ctx.fillStyle = '#ffffff';
        this.ctx.textAlign = 'left';
        this.ctx.fillText(skill.name || '未知功法', margin + 10, itemY + 50);
      }
    }
    
    // 绘制分页信息
    const totalPages = Math.max(1, Math.ceil((this.skills ? this.skills.length : 0) / this.itemsPerPage));
    this.ctx.font = '14px Arial';
    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'center';
    this.ctx.fillText(
      `第 ${this.currentPage + 1}/${totalPages} 页`,
      this.screenWidth / 2,
      this.screenHeight - 20
    );
  }
  
  // 绘制功法详情
  drawSkillDetails() {
    // 如果没有选中的功法，不显示详情
    if (!this.skills || this.selectedSkillIndex < 0 || this.selectedSkillIndex >= this.skills.length) {
      return;
    }
    
    const skill = this.skills[this.selectedSkillIndex];
    if (!skill) return;
    
    const detailsY = this.screenHeight - 200;
    const margin = 10;
    
    // 绘制详情面板背景
    this.ctx.fillStyle = 'rgba(30, 30, 30, 0.9)';
    this.ctx.fillRect(margin, detailsY, this.screenWidth - margin * 2, 100);
    
    try {
      // 绘制详情面板边框
      this.ctx.strokeStyle = skill.getQualityColor ? skill.getQualityColor() : '#ffffff';
      this.ctx.lineWidth = 2;
      this.ctx.strokeRect(margin, detailsY, this.screenWidth - margin * 2, 100);
      
      // 绘制功法描述
      this.ctx.font = '14px Arial';
      this.ctx.fillStyle = '#ffffff';
      this.ctx.textAlign = 'left';
      this.ctx.fillText(skill.description || '无描述', margin + 10, detailsY + 20);
      
      // 绘制功法属性详情
      if (typeof skill.getAttributesDescription === 'function') {
        const attributeLines = skill.getAttributesDescription().split('\n');
        attributeLines.forEach((line, index) => {
          this.ctx.fillText(line, margin + 10, detailsY + 45 + index * 20);
        });
      } else if (skill.attributes) {
        // 如果没有获取属性描述的方法，手动生成描述
        let yOffset = 45;
        if (skill.attributes.hp) {
          this.ctx.fillText(`增加生命值: ${skill.attributes.hp}`, margin + 10, detailsY + yOffset);
          yOffset += 20;
        }
        if (skill.attributes.attack) {
          this.ctx.fillText(`增加攻击力: ${skill.attributes.attack}`, margin + 10, detailsY + yOffset);
          yOffset += 20;
        }
        if (skill.attributes.defense) {
          this.ctx.fillText(`增加防御力: ${skill.attributes.defense}`, margin + 10, detailsY + yOffset);
        }
      }
    } catch (error) {
      console.error('绘制功法详情时出错:', error);
      // 出错时显示基本信息
      this.ctx.font = '14px Arial';
      this.ctx.fillStyle = '#ffffff';
      this.ctx.textAlign = 'center';
      this.ctx.fillText('功法详情显示出错', this.screenWidth / 2, detailsY + 50);
    }
  }
}

export default SkillScene; 