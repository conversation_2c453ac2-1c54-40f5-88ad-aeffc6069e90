/**
 * 主页面场景类
 * 游戏的主界面，包含顶部信息栏和底部导航栏
 */
import BaseScene from './BaseScene';
import Button from '../ui/Button';
import game from '../../game';
import TitleBar from '../ui/TitleBar.js';
// 导入静室场景以便访问静态方法
import JingshiScene from './JingshiScene';

class MainScene extends BaseScene {
  constructor(ctx, screenWidth, screenHeight, sceneManager,resources) {
    super(ctx, screenWidth, screenHeight, sceneManager);
    // 场景资源
    this.resources = resources || {};

    // 背景滚动位置
    this.bgScrollY = 0;
    this.isDragging = false;
    this.lastY = 0;

    // 当前选中的底部导航项
    this.selectedTabIndex = 0;

    // 顶部导航栏
    this.titleBar = null;

    // 初始化UI
    this.initUI();
  }

  // 初始化UI
  initUI() {
    // 首先清空已有的UI元素
    this.clearUIElements();

    console.log('MainScene初始化UI');

    // 获取资源
    if (!this.resources) {
      this.resources = game.resourceLoader ? game.resourceLoader.resources || {} : {};
    }

    // 创建顶部标题栏
    const headerHeight = 80;
    this.titleBar = new TitleBar(
      this.ctx,
      0,
      0,
      this.screenWidth,
      headerHeight,
      this.resources
    );
    this.addUIElement(this.titleBar);

    // 底部导航栏按钮
    const tabBarHeight = 60;
    const tabBarY = this.screenHeight - tabBarHeight;
    const tabButtonWidth = this.screenWidth / 5;

    // 创建底部导航栏按钮
    this.tabButtons = [
      this.createTabButton(0 * tabButtonWidth, tabBarY, tabButtonWidth, tabBarHeight, '主页', 0),
      this.createTabButton(1 * tabButtonWidth, tabBarY, tabButtonWidth, tabBarHeight, '角色', 1),
      this.createTabButton(2 * tabButtonWidth, tabBarY, tabButtonWidth, tabBarHeight, '洞府', 2),
      this.createTabButton(3 * tabButtonWidth, tabBarY, tabButtonWidth, tabBarHeight, '试炼', 3),
      this.createTabButton(4 * tabButtonWidth, tabBarY, tabButtonWidth, tabBarHeight, '背包', 4)
    ];

    // 将按钮添加到UI元素列表
    this.tabButtons.forEach(button => {
      this.addUIElement(button);
    });

    // 创建功法按钮 - 添加在主页面上，方便测试
    const actionButtonWidth = 120;
    const actionButtonHeight = 50;
    const margin = 20;

    this.skillButton = new Button(
      this.ctx,
      margin, // X坐标靠左
      tabBarY - actionButtonHeight - margin, // Y坐标在底部导航栏上方
      actionButtonWidth,
      actionButtonHeight,
      '功法',
      null,
      null,
      () => {
        this.sceneManager.showScene('skill');
      }
    );
    this.addUIElement(this.skillButton);

    // 创建保存按钮
    this.saveButton = new Button(
      this.ctx,
      this.screenWidth - actionButtonWidth - margin, // X坐标靠右
      tabBarY - actionButtonHeight - margin, // Y坐标与功法按钮平齐
      actionButtonWidth,
      actionButtonHeight,
      '保存数据',
      null,
      null,
      () => {
        this.savePlayerData(); // 调用保存方法
      }
    );
    this.addUIElement(this.saveButton);

    // 创建登录按钮
    this.loginButton = new Button(
      this.ctx,
      this.screenWidth / 2 - actionButtonWidth / 2, // X坐标居中
      tabBarY - actionButtonHeight - margin, // Y坐标与其他按钮平齐
      actionButtonWidth,
      actionButtonHeight,
      '登录游戏',
      null,
      null,
      () => {
        this.loginUser(); // 调用登录方法
      }
    );
    this.addUIElement(this.loginButton);

    // 创建主页功能按钮
    this.createMainButtons();

    console.log('MainScene底部导航栏按钮创建完成，数量：', this.tabButtons.length);
  }

  // 创建底部导航栏按钮
  createTabButton(x, y, width, height, text, index) {
    return new Button(
      this.ctx,
      x,
      y,
      width,
      height,
      text,
      null, // 暂时没有按钮资源，使用默认绘制
      null,
      () => {
        this.onTabSelected(index);
      }
    );
  }

  // 底部导航栏选中回调
  onTabSelected(index) {
    // 如果点击的是当前选中的项，不做处理
    if (this.selectedTabIndex === index) {
      return;
    }

    // 更新选中的索引
    this.selectedTabIndex = index;

    // 根据索引切换场景
    switch (index) {
      case 0:
        // 主页，已经在主页，不需要切换
        break;
      case 1:
        // 角色页面 - 直接进入女剑仙角色详情页
        this.sceneManager.showScene('characterDetail', { characterId: 1 });
        break;
      case 2:
        // 洞府页面
        this.sceneManager.showScene('dongfu');
        break;
      case 3:
        // 试炼页面
        this.sceneManager.showScene('trial');
        break;
      case 4:
        // 背包页面
        this.sceneManager.showScene('backpack');
        break;
    }
  }

  // 场景显示时的回调
  onShow(params) {
    console.log('MainScene.onShow被调用，参数：', params);

    // 从其他场景返回时，设置正确的选中标签
    if (params && params.from) {
      switch (params.from) {
        case 'index':
          this.selectedTabIndex = 0;
          break;
        case 'character':
          this.selectedTabIndex = 1;
          break;
        case 'dongfu':
          this.selectedTabIndex = 2;
          break;
        case 'trial':
          this.selectedTabIndex = 3;
          break;
        case 'backpack':
          this.selectedTabIndex = 4;
          break;
      }
    }

    // 清空UI元素
    this.clearUIElements();

    // 初始化UI
    this.initUI();

    // 确保TitleBar显示最新的用户信息
    if (this.titleBar) {
      // 重置加载标志，让TitleBar尝试重新加载头像
      this.titleBar.triedLoadingAvatar = false;

      // 手动加载用户头像
      this.titleBar.loadAvatarImage();

      // 如果玩家已经登录，尝试更新TitleBar用户信息
      if (game.user && (game.user.avatarUrl || game.user.nickName)) {
        const userInfo = {
          nickName: game.user.nickName,
          avatarUrl: game.user.avatarUrl
        };
        this.updateTitleBar(userInfo);
      } else if (game.gameStateManager && game.gameStateManager.state.player) {
        // 或者从GameStateManager获取信息
        const player = game.gameStateManager.state.player;
        if (player.avatarUrl || player.nickname) {
          const userInfo = {
            nickName: player.nickname,
            avatarUrl: player.avatarUrl
          };
          this.updateTitleBar(userInfo);
        }
      }
    }

    // 设置为可见
    this.visible = true;
  }

  // 场景隐藏时的回调
  onHide() {
    // 清空UI元素
    this.clearUIElements();

    // 重置拖动状态
    this.isDragging = false;

    // 设置场景为不可见
    this.visible = false;

    console.log('MainScene隐藏');
  }

  // 子类实现的触摸开始事件处理
  handleTouchStart(x, y) {
    // 检查是否在可滚动区域内
    const headerHeight = 80; // 顶部导航栏高度
    const tabBarHeight = 60; // 底部导航栏高度

    if (y > headerHeight && y < this.screenHeight - tabBarHeight) {
      this.isDragging = true;
      this.lastY = y;
      return true;
    }

    return false;
  }

  // 子类实现的触摸移动事件处理
  handleTouchMove(x, y) {
    if (this.isDragging) {
      // 计算滚动距离
      const deltaY = y - this.lastY;
      this.bgScrollY -= deltaY;

      // 限制滚动范围
      const maxScroll = 500; // 最大滚动距离
      this.bgScrollY = Math.max(0, Math.min(this.bgScrollY, maxScroll));

      this.lastY = y;
      return true;
    }

    return false;
  }

  // 子类实现的触摸结束事件处理
  handleTouchEnd(x, y) {
    if (this.isDragging) {
      this.isDragging = false;
      return true;
    }

    return false;
  }

  // 子类实现的更新逻辑
  updateScene() {
    // 如果已初始化，更新标题栏
    if (this.titleBar) {
      this.titleBar.update();
    }

    // 检查和更新静室修炼进度
    if (typeof JingshiScene !== 'undefined' && JingshiScene.meditationStarted) {
      JingshiScene.checkAndUpdateMeditation();
    }
  }

  // 子类实现的绘制逻辑
  drawScene() {
    // 绘制背景
    this.drawBackground();

    // 绘制主要功能按钮
    this.drawMainButtons();

    // 绘制底部导航栏
    this.drawTabBar();
  }

  // 绘制背景
  drawBackground() {
    // 如果有背景图资源，使用背景图
    if (this.resources && this.resources.mainBg) {
      try {
        // 计算背景图绘制位置
        const bgWidth = this.screenWidth;
        const bgHeight = this.screenWidth * 2; // 假设背景图长宽比为2:1

        this.ctx.drawImage(
          this.resources.mainBg,
          0,
          -this.bgScrollY,
          bgWidth,
          bgHeight
        );
      } catch (error) {
        console.error('绘制背景图失败', error);
        this.drawDefaultBackground();
      }
    } else {
      // 如果没有背景图资源，使用渐变色背景
      this.drawDefaultBackground();
    }
  }

  // 绘制默认背景
  drawDefaultBackground() {
    const gradient = this.ctx.createLinearGradient(0, 0, 0, this.screenHeight);
    gradient.addColorStop(0, '#1a2a6c');
    gradient.addColorStop(0.5, '#b21f1f');
    gradient.addColorStop(1, '#fdbb2d');

    this.ctx.fillStyle = gradient;
    this.ctx.fillRect(0, 0, this.screenWidth, this.screenHeight);
  }

  // 绘制顶部导航栏
  drawHeader() {
    // 该方法已被TitleBar组件替代，不再需要执行任何操作
    // 为了兼容性保留方法
  }

  // 创建主页功能按钮
  createMainButtons() {
    const headerHeight = 80;
    const buttonSize = 70; // 稍微减小按钮尺寸，以便放下更多按钮
    const margin = 15;
    const startY = headerHeight + 60;
    const tabBarHeight = 60; // 添加底部导航栏高度定义

    // 主线关卡按钮 - 左上
    this.storyButton = new Button(
      this.ctx,
      this.screenWidth / 2 - buttonSize - margin,
      startY,
      buttonSize,
      buttonSize,
      '主线关卡',
      null,
      null,
      () => {
        console.log('点击主线关卡按钮');
        this.sceneManager.showScene('story');
      }
    );
    this.addUIElement(this.storyButton);

    // 角色按钮 - 右上
    this.characterButton = new Button(
      this.ctx,
      this.screenWidth / 2 + margin,
      startY,
      buttonSize,
      buttonSize,
      '角色',
      null,
      null,
      () => {
        console.log('点击角色按钮');
        this.sceneManager.showScene('characterDetail', { characterId: 1 });
      }
    );
    this.addUIElement(this.characterButton);

    // 试炼按钮 - 左中
    this.trialButton = new Button(
      this.ctx,
      this.screenWidth / 2 - buttonSize - margin,
      startY + buttonSize + margin,
      buttonSize,
      buttonSize,
      '试炼',
      null,
      null,
      () => {
        console.log('点击试炼按钮');
        this.sceneManager.showScene('trial');
      }
    );
    this.addUIElement(this.trialButton);

    // 洞府按钮 - 右中
    this.dongfuButton = new Button(
      this.ctx,
      this.screenWidth / 2 + margin,
      startY + buttonSize + margin,
      buttonSize,
      buttonSize,
      '洞府',
      null,
      null,
      () => {
        console.log('点击洞府按钮');
        this.sceneManager.showScene('dongfu');
      }
    );
    this.addUIElement(this.dongfuButton);

    // 挂机游历按钮 - 左下
    this.idleButton = new Button(
      this.ctx,
      this.screenWidth / 2 - buttonSize - margin,
      startY + (buttonSize + margin) * 2,
      buttonSize,
      buttonSize,
      '挂机游历',
      null,
      null,
      () => {
        console.log('点击挂机游历按钮');
        this.sceneManager.showScene('idle');
      }
    );
    this.addUIElement(this.idleButton);

    // 功法按钮 - 右下
    this.functionButton = new Button(
      this.ctx,
      this.screenWidth / 2 + margin,
      startY + (buttonSize + margin) * 2,
      buttonSize,
      buttonSize,
      '功法',
      null,
      '#a855f7', // 紫色背景，符合功法神秘感
      () => {
        console.log('点击功法按钮');
        this.sceneManager.showScene('function');
      }
    );
    this.addUIElement(this.functionButton);



    // 装备打造按钮 - 底部右侧
    this.forgeButton = new Button(
      this.ctx,
      this.screenWidth / 2 + margin,
      startY + (buttonSize + margin) * 3,
      buttonSize,
      buttonSize,
      '装备打造',
      null,
      null,
      () => {
        console.log('点击装备打造按钮');
        this.sceneManager.showScene('forge');
      }
    );
    this.addUIElement(this.forgeButton);

    // 剑心按钮 - 左侧第五行
    this.swordHeartButton = new Button(
      this.ctx,
      this.screenWidth / 2 - buttonSize - margin,
      startY + (buttonSize + margin) * 4,
      buttonSize,
      buttonSize,
      '剑心',
      null,
      '#4299e1', // 蓝色背景，符合剑心的冷峻感
      () => {
        console.log('点击剑心按钮');
        this.sceneManager.showScene('swordHeart');
      }
    );
    this.addUIElement(this.swordHeartButton);

    // 充值按钮 - 右侧第五行
    this.rechargeButton = new Button(
      this.ctx,
      this.screenWidth / 2 + margin,
      startY + (buttonSize + margin) * 4,
      buttonSize,
      buttonSize,
      '充值',
      null,
      '#FF9800', // 橙色背景，突出显示
      () => {
        console.log('点击充值按钮');
        this.sceneManager.showScene('recharge');
      }
    );
    this.addUIElement(this.rechargeButton);

    // 剑心抽卡按钮 - 第六行左侧
    this.swordHeartGachaButton = new Button(
      this.ctx,
      this.screenWidth / 2 - buttonSize - margin,
      startY + (buttonSize + margin) * 5,
      buttonSize,
      buttonSize,
      '剑心抽卡',
      null,
      '#805AD5', // 紫色背景，神秘感
      () => {
        console.log('点击剑心抽卡按钮');
        this.sceneManager.showScene('swordHeartGacha');
      }
    );
    this.addUIElement(this.swordHeartGachaButton);

    // 竞技场按钮 - 第六行右侧
    this.arenaButton = new Button(
      this.ctx,
      this.screenWidth / 2 + margin,
      startY + (buttonSize + margin) * 5,
      buttonSize,
      buttonSize,
      '竞技场',
      null,
      '#E53E3E', // 红色背景，战斗感
      () => {
        console.log('点击竞技场按钮');
        this.sceneManager.showScene('arena');
      }
    );
    this.addUIElement(this.arenaButton);

    // 剑骨按钮 - 第七行居中
    this.swordBoneButton = new Button(
      this.ctx,
      this.screenWidth / 2 - buttonSize / 2,
      startY + (buttonSize + margin) * 6,
      buttonSize,
      buttonSize,
      '剑骨',
      null,
      '#2B6CB0', // 蓝色背景，神秘感
      () => {
        console.log('点击剑骨按钮');
        this.sceneManager.showScene('swordBone');
      }
    );
    this.addUIElement(this.swordBoneButton);
  }

  // 绘制主页功能按钮
  drawMainButtons() {
    // 确保清除之前的按钮
    if (this.buttons) {
      this.buttons.forEach(buttonObj => {
        if (buttonObj.button) {
          buttonObj.button.destroy();
        }
        if (buttonObj.text) {
          buttonObj.text.destroy();
        }
      });
      this.buttons = [];
    }

    // 此处不需要额外绘制，因为MainScene的initUI方法已经创建了所有必要的按钮
    // 主页功能按钮已经在createMainButtons方法中创建并添加到UI元素列表中
    // 这些按钮会自动绘制，不需要在这里手动绘制
  }

  // 绘制底部导航栏
  drawTabBar() {
    const tabBarHeight = 60;
    const tabBarY = this.screenHeight - tabBarHeight;

    // 绘制底部导航栏背景
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
    this.ctx.fillRect(0, tabBarY, this.screenWidth, tabBarHeight);

    // 绘制分隔线
    this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.2)';
    this.ctx.lineWidth = 1;
    this.ctx.beginPath();
    this.ctx.moveTo(0, tabBarY);
    this.ctx.lineTo(this.screenWidth, tabBarY);
    this.ctx.stroke();

    // 绘制导航按钮
    const tabButtonWidth = this.screenWidth / 5;
    const tabIcons = ['home', 'character', 'dongfu', 'trial', 'backpack'];
    const tabTexts = ['主页', '角色', '洞府', '试炼', '背包'];

    for (let i = 0; i < 5; i++) {
      const x = i * tabButtonWidth;
      const y = tabBarY;

      // 绘制选中效果
      if (i === this.selectedTabIndex) {
        // 绘制选中指示器
        this.ctx.fillStyle = 'rgba(255, 255, 255, 0.2)';
        this.ctx.fillRect(x, y, tabButtonWidth, tabBarHeight);

        // 绘制选中标记
        this.ctx.fillStyle = '#ffffff';
        this.ctx.fillRect(x + tabButtonWidth / 2 - 15, y + 2, 30, 3);
      }

      // 绘制图标
      const iconSize = 24;
      const iconX = x + (tabButtonWidth - iconSize) / 2;
      const iconY = y + 10;

      // 尝试绘制图标
      const iconKey = `icon${tabIcons[i].charAt(0).toUpperCase() + tabIcons[i].slice(1)}`;
      this.drawTabIcon(iconKey, iconX, iconY, iconSize, tabIcons[i], i === this.selectedTabIndex);

      // 绘制文本
      this.ctx.font = '12px Arial';
      this.ctx.fillStyle = i === this.selectedTabIndex ? '#ffffff' : '#cccccc';
      this.ctx.textAlign = 'center';
      this.ctx.textBaseline = 'top';
      this.ctx.fillText(tabTexts[i], x + tabButtonWidth / 2, y + iconSize + 15);
    }
  }

  // 绘制底部导航栏图标
  drawTabIcon(iconKey, x, y, size, tabType, isSelected) {
    if (this.resources && this.resources[iconKey]) {
      try {
        this.ctx.drawImage(
          this.resources[iconKey],
          x,
          y,
          size,
          size
        );
      } catch (error) {
        console.error(`绘制导航图标 ${iconKey} 失败`, error);
        this.drawDefaultTabIcon(x, y, size, tabType, isSelected);
      }
    } else {
      // 如果没有图标资源，绘制默认图标
      this.drawDefaultTabIcon(x, y, size, tabType, isSelected);
    }
  }

  // 绘制默认底部导航栏图标
  drawDefaultTabIcon(x, y, size, tabType, isSelected) {
    // 设置图标颜色
    this.ctx.fillStyle = isSelected ? '#ffffff' : '#cccccc';

    // 根据标签类型绘制不同的简单图形
    switch (tabType) {
      case 'home':
        // 绘制一个简单的房子图标
        this.ctx.beginPath();
        this.ctx.moveTo(x + size / 2, y);
        this.ctx.lineTo(x, y + size / 2);
        this.ctx.lineTo(x + size / 4, y + size / 2);
        this.ctx.lineTo(x + size / 4, y + size);
        this.ctx.lineTo(x + 3 * size / 4, y + size);
        this.ctx.lineTo(x + 3 * size / 4, y + size / 2);
        this.ctx.lineTo(x + size, y + size / 2);
        this.ctx.closePath();
        this.ctx.fill();
        break;

      case 'character':
        // 绘制一个简单的人物图标
        this.ctx.beginPath();
        this.ctx.arc(x + size / 2, y + size / 3, size / 4, 0, Math.PI * 2);
        this.ctx.fill();

        this.ctx.beginPath();
        this.ctx.moveTo(x + size / 2, y + size / 2);
        this.ctx.lineTo(x + size / 4, y + size);
        this.ctx.lineTo(x + 3 * size / 4, y + size);
        this.ctx.closePath();
        this.ctx.fill();
        break;

      case 'dongfu':
        // 绘制一个简单的洞府图标（山洞）
        this.ctx.beginPath();
        this.ctx.moveTo(x, y + size);
        this.ctx.lineTo(x, y + size / 2);
        this.ctx.quadraticCurveTo(x + size / 2, y, x + size, y + size / 2);
        this.ctx.lineTo(x + size, y + size);
        this.ctx.closePath();
        this.ctx.fill();
        break;

      case 'trial':
        // 绘制一个简单的宝剑图标
        this.ctx.beginPath();
        this.ctx.moveTo(x + size / 2, y);
        this.ctx.lineTo(x + size / 3, y + size * 0.8);
        this.ctx.lineTo(x + size * 2 / 3, y + size * 0.8);
        this.ctx.closePath();
        this.ctx.fill();

        this.ctx.fillRect(x + size / 3, y + size * 0.8, size / 3, size * 0.2);
        break;

      case 'backpack':
        // 绘制一个简单的背包图标
        this.ctx.beginPath();
        this.ctx.moveTo(x + size * 0.2, y + size * 0.3);
        this.ctx.lineTo(x + size * 0.8, y + size * 0.3);
        this.ctx.lineTo(x + size * 0.8, y + size * 0.9);
        this.ctx.lineTo(x + size * 0.2, y + size * 0.9);
        this.ctx.closePath();
        this.ctx.fill();

        // 绘制背包提手
        this.ctx.beginPath();
        this.ctx.moveTo(x + size * 0.3, y + size * 0.3);
        this.ctx.quadraticCurveTo(x + size / 2, y + size * 0.1, x + size * 0.7, y + size * 0.3);
        this.ctx.stroke();
        break;
    }
  }

  /**
   * 添加登录方法
   */
  loginUser() {
    if (!game || !game.cloudInited) {
      console.error('云环境未初始化，无法登录');
      wx.showToast({
        title: '云环境未初始化',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    console.log('尝试登录用户...');
    wx.showLoading({
      title: '登录中...',
      mask: true
    });

    // 即使用户已有openid，也应该从服务器获取最新数据
    if (game.user && game.user.openid) {
      console.log('用户已有openid，正在获取最新数据:', game.user.openid);
      this.loadUserDataFromCloud(game.user.openid);
      return;
    }

    // 直接检查本地存储中的授权信息
    try {
      const userInfo = wx.getStorageSync('userInfo');
      if (userInfo && userInfo.authorized) {
        console.log('用户已授权，直接登录');
        this.proceedToLogin();
        return;
      }
    } catch (err) {
      console.error('获取本地存储授权信息失败', err);
    }

    // 检查game对象中是否已有授权标记
    if (game.userAuthorized) {
      console.log('game.userAuthorized为true，直接登录');
      this.proceedToLogin();
      return;
    }

    // 使用wx.getSetting检查
    wx.getSetting({
      success: (res) => {
        const authSetting = res.authSetting;
        console.log('获取授权设置结果:', authSetting);

        // 检查'scope.userInfo'权限
        if (authSetting && authSetting['scope.userInfo'] === true) {
          console.log('用户已授权用户信息，直接登录');
          this.proceedToLogin();
        } else {
          // 未授权，显示授权请求
          wx.hideLoading();
          console.log('用户未授权，显示授权请求');
          this.requestUserInfo();
        }
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('获取用户授权设置失败:', err);
        // 失败时也尝试直接请求授权
        this.requestUserInfo();
      }
    });
  }

  /**
   * 从云端加载用户数据
   */
  loadUserDataFromCloud(openid) {
    console.log('从云端加载用户数据，openid:', openid);

    // 查询用户是否已经在数据库中存在
    game.db.collection('xiuxian-player').where({
      openid: openid
    }).get().then(res => {
      wx.hideLoading();

      if (res.data && res.data.length > 0) {
        console.log('云端发现用户数据，载入用户数据');
        const userData = res.data[0];

        // 更新本地用户信息
        game.user = { ...game.user, ...userData };

        // 如果有gameState数据，载入到游戏状态
        if (userData.gameState) {
          console.log('从云端载入游戏数据:', userData.gameState);

          try {
            // 在加载前记录之前的状态，用于日志对比
            const previousResources = game.gameStateManager.state.player?.resources
              ? JSON.stringify(game.gameStateManager.state.player.resources)
              : 'undefined';

            // 加载游戏状态
            game.gameStateManager.loadGameState(userData.gameState);

            // 确保资源数据正确同步
            if (userData.gameState.player && userData.gameState.player.resources) {
              console.log('同步玩家资源数据:', userData.gameState.player.resources);

              // 确保player对象存在
              if (!game.gameStateManager.state.player) {
                game.gameStateManager.state.player = {};
              }

              // 直接强制更新资源数据
              game.gameStateManager.state.player.resources = { ...userData.gameState.player.resources };

              // 记录资源更新日志
              console.log('资源数据更新:', '之前:', previousResources, '之后:', JSON.stringify(game.gameStateManager.state.player.resources));

              // 触发玩家数据更新事件
              game.gameStateManager.emit('playerDataChanged', game.gameStateManager.state.player);
            }
          } catch (error) {
            console.error('载入游戏数据失败:', error);
          }
        }

        wx.showToast({
          title: '数据同步成功',
          icon: 'success',
          duration: 2000
        });

        // 强制保存游戏状态
        game.gameStateManager.saveGameState();

        // 刷新主场景UI
        this.clearUIElements();
        this.initUI();

        // 手动调用绘制方法确保UI更新
        this.drawScene();
      } else {
        console.log('云端未找到用户数据，保存当前数据');
        // 保存当前游戏数据到云端
        this.savePlayerData();

        wx.showToast({
          title: '数据已上传',
          icon: 'success',
          duration: 2000
        });
      }
    }).catch(err => {
      wx.hideLoading();
      console.error('查询用户数据失败', err);
      wx.showToast({
        title: '数据同步失败',
        icon: 'none',
        duration: 2000
      });
    });
  }

  /**
   * 请求用户信息和授权
   */
  requestUserInfo() {
    // 使用微信原生的getUserProfile API
    wx.getUserProfile({
      desc: '用于完善会员资料', // 声明获取用户个人信息后的用途
      lang: 'zh_CN',
      success: (res) => {
        console.log('获取用户信息成功:', res.userInfo);

        // 保存用户信息
        this.setUserInfo(res.userInfo);

        // 继续登录流程
        this.proceedToLogin();
      },
      fail: (err) => {
        console.error('用户拒绝授权:', err);
        wx.showModal({
          title: '授权提醒',
          content: '需要您的授权才能保存游戏进度，请重新点击按钮并允许授权',
          showCancel: false,
          confirmText: '我知道了'
        });
      }
    });
  }

  /**
   * 保存用户信息
   */
  setUserInfo(userInfo) {
    // 确保game.user存在
    game.user = game.user || {};

    // 更新用户信息
    game.user.nickName = userInfo.nickName;
    game.user.avatarUrl = userInfo.avatarUrl;
    game.user.gender = userInfo.gender;
    game.userAuthorized = true;

    // 将用户信息同步到GameStateManager
    if (game.gameStateManager) {
      if (!game.gameStateManager.state.player) {
        game.gameStateManager.state.player = {};
      }

      // 确保player对象包含基本信息
      game.gameStateManager.state.player.nickname = userInfo.nickName || '无名修士';
      game.gameStateManager.state.player.avatarUrl = userInfo.avatarUrl;

      console.log('已将用户信息同步到GameStateManager', {
        nickname: game.gameStateManager.state.player.nickname
      });

      // 更新GameStateManager中的玩家数据
      game.gameStateManager.setPlayer(game.gameStateManager.state.player);
    }

    // 将用户数据存储到本地以便下次启动使用
    try {
      wx.setStorageSync('userInfo', {
        nickName: userInfo.nickName,
        avatarUrl: userInfo.avatarUrl,
        gender: userInfo.gender,
        authorized: true,
        timestamp: Date.now()
      });
      console.log('用户信息已保存到本地存储');
    } catch (err) {
      console.error('保存用户信息到本地失败:', err);
    }

    // 更新TitleBar显示
    this.updateTitleBar(userInfo);
  }

  /**
   * 更新TitleBar显示
   */
  updateTitleBar(userInfo) {
    // 查找TitleBar组件并更新
    if (this.titleBar && this.titleBar.updateUserInfo) {
      console.log('正在更新TitleBar用户信息');
      this.titleBar.updateUserInfo(userInfo);
    } else {
      console.warn('未找到TitleBar组件或updateUserInfo方法');
    }
  }

  /**
   * 继续登录流程
   */
  proceedToLogin() {
    // 获取用户OpenID
    game.cloud.callFunction({
      name: 'login',
      success: res => {
        console.log('获取用户OpenID成功', res.result);
        const openid = res.result.openid;
        if (openid) {
          // 设置用户基本信息
          game.user = game.user || {};
          game.user.openid = openid;

          // 同步openid到玩家数据
          if (game.gameStateManager && game.gameStateManager.state && game.gameStateManager.state.player) {
            game.gameStateManager.state.player.openid = openid;
          }

          // 查询用户是否已经在数据库中存在
          game.db.collection('xiuxian-player').where({
            openid: openid
          }).get().then(res => {
            wx.hideLoading();

            if (res.data && res.data.length > 0) {
              console.log('用户数据已存在，载入用户数据');
              const userData = res.data[0];

              // 更新本地用户信息
              game.user = { ...game.user, ...userData };

              // 如果有gameState数据，载入到游戏状态
              if (userData.gameState) {
                console.log('从云端载入游戏数据:', userData.gameState);

                try {
                  // 在加载前记录之前的状态，用于日志对比
                  const previousResources = game.gameStateManager.state.player?.resources
                    ? JSON.stringify(game.gameStateManager.state.player.resources)
                    : 'undefined';

                  // 加载游戏状态
                  game.gameStateManager.loadGameState(userData.gameState);

                  // 确保资源数据正确同步
                  if (userData.gameState.player && userData.gameState.player.resources) {
                    console.log('同步玩家资源数据:', userData.gameState.player.resources);

                    // 确保player对象存在
                    if (!game.gameStateManager.state.player) {
                      game.gameStateManager.state.player = {};
                    }

                    // 直接强制更新资源数据
                    game.gameStateManager.state.player.resources = { ...userData.gameState.player.resources };

                    // 记录资源更新日志
                    console.log('资源数据更新:', '之前:', previousResources, '之后:', JSON.stringify(game.gameStateManager.state.player.resources));

                    // 触发玩家数据更新事件
                    game.gameStateManager.emit('playerDataChanged', game.gameStateManager.state.player);
                  }

                  // 更新TitleBar显示
                  if (userData.gameState.player) {
                    const userInfo = {
                      nickName: userData.gameState.player.nickname || userData.nickName,
                      avatarUrl: userData.gameState.player.avatarUrl || userData.avatarUrl
                    };
                    this.updateTitleBar(userInfo);
                  }
                } catch (error) {
                  console.error('载入游戏数据失败:', error);
                }
              }

              wx.showToast({
                title: '登录成功',
                icon: 'success',
                duration: 2000
              });

              // 强制保存游戏状态
              game.gameStateManager.saveGameState();

              // 刷新主场景UI
              this.clearUIElements();
              this.initUI();

              // 手动调用绘制方法确保UI更新
              this.drawScene();
            } else {
              console.log('用户数据不存在，创建新数据');
              // 保存当前游戏数据到云端
              this.savePlayerData();

              wx.showToast({
                title: '欢迎新玩家',
                icon: 'success',
                duration: 2000
              });
            }
          }).catch(err => {
            wx.hideLoading();
            console.error('查询用户数据失败', err);
            wx.showToast({
              title: '登录失败',
              icon: 'none',
              duration: 2000
            });
          });
        } else {
          wx.hideLoading();
          console.error('获取用户OpenID为空');
          wx.showToast({
            title: '登录失败',
            icon: 'none',
            duration: 2000
          });
        }
      },
      fail: err => {
        wx.hideLoading();
        console.error('获取用户OpenID失败', err);
        wx.showToast({
          title: '登录失败',
          icon: 'none',
          duration: 2000
        });
      }
    });
  }

  /**
   * 保存玩家数据到云数据库
   */
  async savePlayerData() {
    if (!game || !game.cloudInited) {
      console.error('云环境未初始化，无法保存数据');
      wx.showToast({
        title: '云环境未初始化',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    // 检查用户是否已登录（有openid）
    if (!game.user || !game.user.openid) {
      console.log('用户未登录，先执行登录流程');
      this.loginUser();
      return;
    }

    // 获取游戏状态
    const gameStateManager = game.gameStateManager;
    if (!gameStateManager) {
      console.error('游戏状态管理器未初始化');
      return;
    }

    // 确保openid已同步到玩家数据
    if (gameStateManager.state && gameStateManager.state.player) {
      gameStateManager.state.player.openid = game.user.openid;
    }

    // 获取完整的游戏状态数据
    let playerData;
    try {
      playerData = gameStateManager.getFullStateForSave();
    } catch (error) {
      console.error('获取游戏状态数据失败:', error);
      wx.showToast({
        title: '无法获取游戏数据',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    // 使用game.user中的openid
    const openid = game.user.openid;

    console.log('准备保存数据到云数据库', { openid, dataSize: JSON.stringify(playerData).length });
    wx.showLoading({
      title: '保存中...',
      mask: true
    });

    // 确保playerData中包含OpenID，并删除可能会导致问题的字段
    playerData.openid = openid;
    // 删除_openid字段(如果存在)，这个字段会导致保存失败
    if (playerData._openid) {
      console.log('删除可能导致问题的_openid字段');
      delete playerData._openid;
    }

    // 查询是否已有记录
    try {
      // 使用openid字段而不是_openid字段查询
      const queryResult = await game.db.collection('xiuxian-player').where({
        openid: openid  // 使用普通字段 openid 而不是 _openid
      }).get();

      if (queryResult.data && queryResult.data.length > 0) {
        // 更新记录
        console.log('找到现有记录，进行更新');
        const recordId = queryResult.data[0]._id;
        await game.db.collection('xiuxian-player').doc(recordId).update({
          data: {
            gameState: playerData,
            updateTime: new Date()
          }
        });
      } else {
        // 添加记录 - 不要设置 _openid 字段
        console.log('没有找到记录，创建新记录');
        await game.db.collection('xiuxian-player').add({
          data: {
            openid: openid,  // 作为普通字段存储
            gameState: playerData,
            createTime: new Date(),
            updateTime: new Date()
          }
        });
      }

      console.log('保存成功');
      wx.hideLoading();
      wx.showToast({
        title: '保存成功',
        icon: 'success',
        duration: 2000
      });
    } catch (err) {
      console.error('保存失败', err);
      wx.hideLoading();
      wx.showToast({
        title: '保存失败: ' + err.message,
        icon: 'none',
        duration: 2000
      });
    }
  }
}

export default MainScene;